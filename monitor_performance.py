#!/usr/bin/env python3
"""
Real-time performance monitoring script for the trading bot system.
Tracks P&L, signal quality, and system health in real-time.
"""

import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

# Import unified configuration
from config import get_logs_path, get_monitoring_path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealTimePerformanceMonitor:
    """Real-time performance monitoring for trading bot system."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.last_update = datetime.now()
        self.monitoring_data = {
            'signals': {'buy': 0, 'sell': 0, 'hold': 0},
            'trades': {'executed': 0, 'successful': 0, 'failed': 0},
            'pnl': {'total': 0.0, 'by_terminal': {}},
            'errors': {'count': 0, 'recent': []},
            'system_health': {'status': 'UNKNOWN', 'uptime': 0}
        }
        
    def analyze_recent_logs(self, hours: int = 1) -> Dict[str, Any]:
        """Analyze recent log files for performance data."""
        try:
            logs_dir = get_logs_path()
            log_files = [
                str(logs_dir / 'main.log'),
                str(logs_dir / 'error.log'),
                str(logs_dir / 'terminals' / 'terminal_1.log'),
                str(logs_dir / 'terminals' / 'terminal_2.log'),
                str(logs_dir / 'terminals' / 'terminal_3.log'),
                str(logs_dir / 'terminals' / 'terminal_4.log'),
                str(logs_dir / 'terminals' / 'terminal_5.log')
            ]
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            analysis = {
                'signals_generated': 0,
                'buy_signals': 0,
                'sell_signals': 0,
                'hold_signals': 0,
                'trades_executed': 0,
                'errors_found': 0,
                'warnings_found': 0,
                'confidence_filtered': 0,
                'model_predictions': 0
            }
            
            for log_file in log_files:
                if Path(log_file).exists():
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            for line in f:
                                if 'Signal decision:' in line:
                                    analysis['signals_generated'] += 1
                                    if 'BUY' in line:
                                        analysis['buy_signals'] += 1
                                    elif 'SELL' in line:
                                        analysis['sell_signals'] += 1
                                    elif 'HOLD' in line:
                                        analysis['hold_signals'] += 1
                                
                                if 'confidence' in line and 'below threshold' in line:
                                    analysis['confidence_filtered'] += 1
                                
                                if 'Trade executed:' in line:
                                    analysis['trades_executed'] += 1
                                
                                if 'ERROR' in line:
                                    analysis['errors_found'] += 1
                                
                                if 'WARNING' in line:
                                    analysis['warnings_found'] += 1
                                
                                if 'prediction:' in line:
                                    analysis['model_predictions'] += 1
                    
                    except Exception as e:
                        logger.error(f"CRITICAL: Error reading log file {log_file}: {str(e)}")
                        # CRITICAL FIX: Log file reading errors should be more visible
                        # This could indicate disk issues or permission problems
                        analysis['log_read_errors'] = analysis.get('log_read_errors', 0) + 1
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing logs: {str(e)}")
            return {}
    
    def get_terminal_performance(self) -> Dict[str, Any]:
        """Get performance data from terminal monitoring files."""
        try:
            performance_data = {}
            
            # Check for monitoring snapshots
            monitoring_dir = get_monitoring_path()
            if monitoring_dir.exists():
                snapshot_files = list(monitoring_dir.glob('metrics_snapshot_*.json'))
                if snapshot_files:
                    # Get the most recent snapshot
                    latest_snapshot = max(snapshot_files, key=lambda x: x.stat().st_mtime)
                    try:
                        with open(latest_snapshot, 'r') as f:
                            snapshot_data = json.load(f)
                            performance_data['latest_snapshot'] = snapshot_data
                            performance_data['snapshot_time'] = latest_snapshot.name
                    except Exception as e:
                        logger.debug(f"Error reading snapshot {latest_snapshot}: {str(e)}")
            
            return performance_data
            
        except Exception as e:
            logger.error(f"Error getting terminal performance: {str(e)}")
            return {}
    
    def calculate_signal_quality(self, analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate signal quality metrics."""
        try:
            total_signals = analysis.get('signals_generated', 0)
            if total_signals == 0:
                return {'quality_score': 0.0, 'diversity_score': 0.0, 'filter_rate': 0.0}
            
            # Calculate diversity (how balanced are buy/sell signals)
            buy_ratio = analysis.get('buy_signals', 0) / total_signals
            sell_ratio = analysis.get('sell_signals', 0) / total_signals
            hold_ratio = analysis.get('hold_signals', 0) / total_signals
            
            # Diversity score (closer to 0.33/0.33/0.33 is better)
            ideal_ratio = 1/3
            diversity_score = 1.0 - abs(buy_ratio - ideal_ratio) - abs(sell_ratio - ideal_ratio) - abs(hold_ratio - ideal_ratio)
            diversity_score = max(0.0, diversity_score)
            
            # Filter rate (how many signals are filtered out due to low confidence)
            filtered = analysis.get('confidence_filtered', 0)
            filter_rate = filtered / (total_signals + filtered) if (total_signals + filtered) > 0 else 0.0
            
            # Quality score (combination of factors)
            quality_score = diversity_score * 0.7 + (1.0 - filter_rate) * 0.3
            
            return {
                'quality_score': round(quality_score, 3),
                'diversity_score': round(diversity_score, 3),
                'filter_rate': round(filter_rate, 3),
                'buy_ratio': round(buy_ratio, 3),
                'sell_ratio': round(sell_ratio, 3),
                'hold_ratio': round(hold_ratio, 3)
            }
            
        except Exception as e:
            logger.error(f"Error calculating signal quality: {str(e)}")
            return {}
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive real-time performance report."""
        try:
            current_time = datetime.now()
            uptime = current_time - self.start_time
            
            # Analyze recent activity
            recent_analysis = self.analyze_recent_logs(hours=1)
            terminal_performance = self.get_terminal_performance()
            signal_quality = self.calculate_signal_quality(recent_analysis)
            
            report = {
                'timestamp': current_time.isoformat(),
                'uptime_seconds': int(uptime.total_seconds()),
                'uptime_formatted': str(uptime).split('.')[0],
                'monitoring_period': '1 hour',
                'system_status': self._determine_system_status(recent_analysis),
                'recent_activity': recent_analysis,
                'signal_quality': signal_quality,
                'terminal_performance': terminal_performance,
                'recommendations': self._generate_recommendations(recent_analysis, signal_quality)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def _determine_system_status(self, analysis: Dict[str, Any]) -> str:
        """Determine overall system status based on recent activity."""
        try:
            errors = analysis.get('errors_found', 0)
            signals = analysis.get('signals_generated', 0)
            trades = analysis.get('trades_executed', 0)
            
            if errors > 10:
                return 'CRITICAL'
            elif errors > 5:
                return 'WARNING'
            elif signals > 0 and trades >= 0:
                return 'HEALTHY'
            elif signals > 0:
                return 'ACTIVE'
            else:
                return 'IDLE'
                
        except Exception:
            return 'UNKNOWN'
    
    def _generate_recommendations(self, analysis: Dict[str, Any], quality: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on current performance."""
        recommendations = []
        
        try:
            # Check signal bias
            total_signals = analysis.get('signals_generated', 0)
            if total_signals > 0:
                sell_ratio = analysis.get('sell_signals', 0) / total_signals
                if sell_ratio > 0.8:
                    recommendations.append("🚨 HIGH SELL BIAS: 80%+ sell signals detected - review signal generation thresholds")
                elif sell_ratio > 0.6:
                    recommendations.append("⚠️ MODERATE SELL BIAS: Consider adjusting prediction thresholds")
            
            # Check filter rate
            filter_rate = quality.get('filter_rate', 0)
            if filter_rate > 0.7:
                recommendations.append("📊 HIGH FILTER RATE: 70%+ signals filtered - consider lowering confidence threshold")
            elif filter_rate < 0.1:
                recommendations.append("🎯 LOW FILTER RATE: Consider raising confidence threshold for better signal quality")
            
            # Check error rate
            errors = analysis.get('errors_found', 0)
            if errors > 5:
                recommendations.append(f"🔧 HIGH ERROR RATE: {errors} errors in last hour - review error logs")
            
            # Check trading activity
            trades = analysis.get('trades_executed', 0)
            if trades == 0 and total_signals > 0:
                recommendations.append("💼 NO TRADES EXECUTED: Signals generated but no trades - check execution logic")
            
            if not recommendations:
                recommendations.append("✅ SYSTEM OPERATING NORMALLY: No immediate issues detected")
                
        except Exception as e:
            recommendations.append(f"❌ ERROR GENERATING RECOMMENDATIONS: {str(e)}")
        
        return recommendations
    
    def print_report(self, report: Dict[str, Any]):
        """Print formatted performance report to console."""
        try:
            print("\n" + "="*80)
            print("🤖 REAL-TIME TRADING BOT PERFORMANCE MONITOR")
            print("="*80)
            print(f"📅 Timestamp: {report.get('timestamp', 'Unknown')}")
            print(f"⏱️  Uptime: {report.get('uptime_formatted', 'Unknown')}")
            print(f"🔍 Monitoring Period: {report.get('monitoring_period', 'Unknown')}")
            print(f"🚦 System Status: {report.get('system_status', 'Unknown')}")
            
            # Recent Activity
            activity = report.get('recent_activity', {})
            print(f"\n📊 RECENT ACTIVITY (Last Hour):")
            print(f"   📈 Signals Generated: {activity.get('signals_generated', 0)}")
            print(f"   🟢 Buy Signals: {activity.get('buy_signals', 0)}")
            print(f"   🔴 Sell Signals: {activity.get('sell_signals', 0)}")
            print(f"   ⚪ Hold Signals: {activity.get('hold_signals', 0)}")
            print(f"   💼 Trades Executed: {activity.get('trades_executed', 0)}")
            print(f"   🚫 Confidence Filtered: {activity.get('confidence_filtered', 0)}")
            print(f"   ❌ Errors Found: {activity.get('errors_found', 0)}")
            print(f"   ⚠️  Warnings Found: {activity.get('warnings_found', 0)}")
            
            # Signal Quality
            quality = report.get('signal_quality', {})
            if quality:
                print(f"\n🎯 SIGNAL QUALITY METRICS:")
                print(f"   📊 Quality Score: {quality.get('quality_score', 0):.3f}")
                print(f"   🔄 Diversity Score: {quality.get('diversity_score', 0):.3f}")
                print(f"   🚫 Filter Rate: {quality.get('filter_rate', 0):.3f}")
                print(f"   📈 Buy Ratio: {quality.get('buy_ratio', 0):.3f}")
                print(f"   📉 Sell Ratio: {quality.get('sell_ratio', 0):.3f}")
                print(f"   ⚪ Hold Ratio: {quality.get('hold_ratio', 0):.3f}")
            
            # Recommendations
            recommendations = report.get('recommendations', [])
            if recommendations:
                print(f"\n💡 RECOMMENDATIONS:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"   {i}. {rec}")
            
            print("="*80)
            
        except Exception as e:
            print(f"Error printing report: {str(e)}")

def main():
    """Main monitoring loop."""
    monitor = RealTimePerformanceMonitor()
    
    print("🚀 Starting Real-Time Performance Monitor...")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            # Generate and display report
            report = monitor.generate_report()
            monitor.print_report(report)
            
            # Save report to file using standardized path
            monitoring_dir = get_monitoring_path()
            monitoring_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = monitoring_dir / f"realtime_performance_{timestamp}.json"
            try:
                with open(str(report_file), 'w') as f:
                    json.dump(report, f, indent=2)
                print(f"📄 Report saved to: {report_file}")
            except Exception as e:
                print(f"❌ Error saving report: {str(e)}")
            
            # Wait before next update
            print(f"\n⏳ Next update in 60 seconds...")
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"❌ Monitoring error: {str(e)}")

if __name__ == "__main__":
    main()
