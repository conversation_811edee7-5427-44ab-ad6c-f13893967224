#!/usr/bin/env python
"""
Configuration Validation Script

This script validates that the successful model configurations can be replicated
and checks for any missing dependencies or configuration issues.
"""

import json
import os
import sys
import logging
from pathlib import Path
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_successful_configs() -> Dict:
    """Load the successful model configurations."""
    try:
        with open('successful_model_configs.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("successful_model_configs.json not found")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing successful_model_configs.json: {e}")
        return {}

def validate_file_paths(configs: Dict) -> bool:
    """Validate that all required files and directories exist."""
    logger.info("Validating file paths...")

    all_valid = True

    # Check model directories
    model_locations = configs.get('model_file_locations', {})
    for model_type, path in model_locations.items():
        if model_type.endswith('_successful'):
            if not Path(path).exists():
                logger.error(f"Model directory not found: {path}")
                all_valid = False
            else:
                logger.info(f"✓ Found {model_type}: {path}")

    # Check data directory
    data_reqs = configs.get('data_requirements', {})
    data_dir = data_reqs.get('data_directory', '')
    if data_dir and not Path(data_dir).exists():
        logger.warning(f"Data directory not found: {data_dir}")

    # Check training scripts
    training_commands = configs.get('training_commands', {})
    for model_type, command in training_commands.items():
        script_name = command.split()[1] if len(command.split()) > 1 else command.split()[0]
        if not Path(script_name).exists():
            logger.error(f"Training script not found: {script_name}")
            all_valid = False
        else:
            logger.info(f"✓ Found training script for {model_type}: {script_name}")

    return all_valid

def validate_dependencies() -> bool:
    """Validate that all required Python packages are installed."""
    logger.info("Validating dependencies...")

    required_packages = [
        'torch',
        'numpy',
        'pandas',
        'sklearn',
        'matplotlib',
        'statsmodels',
        'pmdarima'
    ]

    optional_packages = [
        'pytorch_lightning',
        'pytorch_forecasting'
    ]

    all_valid = True

    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} is installed")
        except ImportError:
            logger.error(f"✗ Required package not found: {package}")
            all_valid = False

    for package in optional_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package} is installed (optional)")
        except ImportError:
            logger.warning(f"⚠ Optional package not found: {package}")

    return all_valid

def validate_config_consistency() -> bool:
    """Validate consistency between config files."""
    logger.info("Validating configuration consistency...")

    all_valid = True

    # Check main config file
    config_files = ['config/config.json', 'config/config.example']

    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)

                # Check feature columns consistency
                models = config.get('models', {})
                for model_name, model_config in models.items():
                    feature_cols = model_config.get('FEATURE_COLUMNS', [])
                    if 'volume' in feature_cols:
                        logger.warning(f"Found 'volume' instead of 'real_volume' in {config_file} for {model_name}")
                        all_valid = False
                    elif 'real_volume' in feature_cols:
                        logger.info(f"✓ Correct feature columns in {config_file} for {model_name}")

            except Exception as e:
                logger.error(f"Error reading {config_file}: {e}")
                all_valid = False
        else:
            logger.warning(f"Config file not found: {config_file}")

    return all_valid

def validate_model_configs(configs: Dict) -> bool:
    """Validate the actual model configuration files."""
    logger.info("Validating saved model configurations...")

    all_valid = True

    models = configs.get('models', {})
    model_locations = configs.get('model_file_locations', {})

    for model_type in ['lstm', 'arima']:
        model_config = models.get(model_type, {})
        location_key = f"{model_type}_successful"
        model_path = model_locations.get(location_key, '')

        if model_path and Path(model_path).exists():
            config_file = Path(model_path) / 'config.json'
            if config_file.exists():
                try:
                    with open(config_file, 'r') as f:
                        saved_config = json.load(f)

                    # Validate key parameters match
                    if model_type == 'lstm':
                        expected_params = model_config.get('timeframes', {}).get('M5', {})
                        for param in ['sequence_length', 'hidden_size', 'num_layers', 'dropout_rate', 'learning_rate']:
                            if param in expected_params and param in saved_config:
                                if expected_params[param] != saved_config[param]:
                                    logger.warning(f"Parameter mismatch in {model_type}: {param}")
                                    logger.warning(f"  Expected: {expected_params[param]}, Found: {saved_config[param]}")

                    logger.info(f"✓ Validated {model_type} model configuration")

                except Exception as e:
                    logger.error(f"Error reading {config_file}: {e}")
                    all_valid = False
            else:
                logger.error(f"Config file not found: {config_file}")
                all_valid = False

    return all_valid

def generate_replication_script(configs: Dict) -> None:
    """Generate a script to replicate the successful configurations."""
    logger.info("Generating replication script...")

    script_content = """#!/usr/bin/env python
'''
Model Replication Script

This script replicates the successful model configurations.
Generated automatically from validate_successful_configs.py
'''

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command):
    '''Run a command and log the output.'''
    logger.info(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✓ Command completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
        else:
            logger.error(f"✗ Command failed with return code {result.returncode}")
            if result.stderr:
                logger.error(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        logger.error(f"Exception running command: {e}")
        return False

def main():
    '''Main replication function.'''
    logger.info("Starting model replication...")

    # Training commands from successful configurations
"""

    training_commands = configs.get('training_commands', {})
    for model_type, command in training_commands.items():
        script_content += f'    # {model_type.upper()} Model\n'
        script_content += f'    if not run_command("{command}"):\n'
        script_content += f'        logger.error("Failed to train {model_type} model")\n'
        script_content += f'        return False\n\n'

    script_content += """
    logger.info("All models trained successfully!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""

    with open('replicate_successful_models.py', 'w', encoding='utf-8') as f:
        f.write(script_content)

    logger.info("✓ Generated replicate_successful_models.py")

def main():
    """Main validation function."""
    logger.info("Starting configuration validation...")

    # Load configurations
    configs = load_successful_configs()
    if not configs:
        logger.error("Failed to load configurations")
        return False

    # Run all validations
    validations = [
        validate_dependencies(),
        validate_file_paths(configs),
        validate_config_consistency(),
        validate_model_configs(configs)
    ]

    # Generate replication script
    generate_replication_script(configs)

    # Summary
    if all(validations):
        logger.info("✓ All validations passed!")
        logger.info("The successful configurations can be replicated.")
        return True
    else:
        logger.error("✗ Some validations failed.")
        logger.error("Please fix the issues before attempting to replicate the configurations.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
