#!/usr/bin/env python3
"""
PyTorch-based model training script.
Provides unified interface for training models using PyTorch.
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

from utils.enhanced_logging import setup_logging

logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train models using PyTorch')
    
    parser.add_argument('--timeframes', nargs='+', default=['M5'],
                       help='List of timeframes (M5 M15 M30 H1 H4) or "all"')
    parser.add_argument('--models', nargs='+', default=['all'],
                       help='List of models (lstm tft arima) or "all"')
    parser.add_argument('--terminal_id', type=str, default='1',
                       help='MT5 terminal ID')
    parser.add_argument('--data_dir', type=str, default='data/historical/btcusd.a',
                       help='Directory containing the data files')
    parser.add_argument('--symbol', type=str, default='BTCUSD.a',
                       help='Trading symbol')
    parser.add_argument('--force-cpu', action='store_true',
                       help='Force using CPU even if GPU is available')
    
    return parser.parse_args()

def setup_pytorch_environment(force_cpu=False):
    """Setup PyTorch environment."""
    try:
        import torch
        
        if force_cpu:
            # Force CPU usage
            os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
            logger.info("Forcing CPU usage for PyTorch")
        else:
            # Check GPU availability
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                logger.info(f"Found {gpu_count} GPU(s) available for PyTorch")
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    logger.info(f"GPU {i}: {gpu_name}")
            else:
                logger.info("No GPUs found, using CPU for PyTorch")
        
        return True
    except ImportError:
        logger.error("PyTorch not installed. Please install with: pip install torch")
        return False
    except Exception as e:
        logger.error(f"Error setting up PyTorch environment: {str(e)}")
        return False

def train_lstm_models(timeframes, terminal_id, symbol):
    """Train LSTM models using PyTorch."""
    logger.info("Training LSTM models with PyTorch...")
    
    try:
        # Import and run LSTM training
        from train_lstm_btcusd import main as train_lstm_main
        
        for timeframe in timeframes:
            logger.info(f"Training LSTM model for {symbol} on {timeframe}")
            # Call the existing LSTM training script
            train_lstm_main()
            
    except ImportError:
        logger.error("LSTM training script not found. Please ensure train_lstm_btcusd.py exists.")
        return False
    except Exception as e:
        logger.error(f"Error training LSTM models: {str(e)}")
        return False
    
    return True

def train_tft_models(timeframes, terminal_id, symbol):
    """Train TFT models using PyTorch."""
    logger.info("Training TFT models with PyTorch...")
    
    try:
        # Import and run TFT training
        from train_tft_pytorch import main as train_tft_main
        
        for timeframe in timeframes:
            logger.info(f"Training TFT model for {symbol} on {timeframe}")
            # Call the existing TFT training script
            train_tft_main()
            
    except ImportError:
        logger.error("TFT training script not found. Please ensure train_tft_pytorch.py exists.")
        return False
    except Exception as e:
        logger.error(f"Error training TFT models: {str(e)}")
        return False
    
    return True

def train_arima_models(timeframes, terminal_id, symbol):
    """Train ARIMA models."""
    logger.info("Training ARIMA models...")
    
    try:
        # Import and run ARIMA training
        from train_arima_single import main as train_arima_main
        
        for timeframe in timeframes:
            logger.info(f"Training ARIMA model for {symbol} on {timeframe}")
            # Call the existing ARIMA training script
            train_arima_main()
            
    except ImportError:
        logger.error("ARIMA training script not found. Please ensure train_arima_single.py exists.")
        return False
    except Exception as e:
        logger.error(f"Error training ARIMA models: {str(e)}")
        return False
    
    return True

def main():
    """Main training function."""
    # Setup logging
    setup_logging()
    
    # Parse arguments
    args = parse_arguments()
    
    logger.info("Starting PyTorch-based model training...")
    logger.info(f"Arguments: {vars(args)}")
    
    # Setup PyTorch environment
    if not setup_pytorch_environment(args.force_cpu):
        logger.error("Failed to setup PyTorch environment")
        return 1
    
    # Parse timeframes
    if 'all' in args.timeframes:
        timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
    else:
        timeframes = args.timeframes
    
    # Parse models
    if 'all' in args.models:
        models = ['lstm', 'tft', 'arima']
    else:
        models = args.models
    
    logger.info(f"Training models: {models} on timeframes: {timeframes}")
    
    # Train models
    success = True
    
    if 'lstm' in models:
        if not train_lstm_models(timeframes, args.terminal_id, args.symbol):
            success = False
    
    if 'tft' in models:
        if not train_tft_models(timeframes, args.terminal_id, args.symbol):
            success = False
    
    if 'arima' in models:
        if not train_arima_models(timeframes, args.terminal_id, args.symbol):
            success = False
    
    if success:
        logger.info("All model training completed successfully!")
        return 0
    else:
        logger.error("Some model training failed. Check logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
