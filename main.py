"""
Main module for the trading bot.
Handles initialization and execution of trading bots.
"""
import os
import sys
import signal
import logging
import time
import threading
import psutil
from typing import Dict, Optional, Tuple
from pathlib import Path

# Suppress TensorFlow oneDNN warnings
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# Configure comprehensive logging system - FIXED: Prevent logging loops
import logging

# Create logs directory first
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Initialize basic logging first to prevent loops
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/main.log"),
        logging.StreamHandler()
    ]
)

# Get main logger
logger = logging.getLogger(__name__)

# Try to initialize comprehensive logging without causing loops
try:
    from utils.comprehensive_logging import comprehensive_logger, get_main_logger
    from utils.terminal_manager import get_terminal_manager

    # Initialize comprehensive logging ONCE only
    if not hasattr(comprehensive_logger, '_monitoring_started'):
        comprehensive_logger.start_monitoring(interval=60)  # Monitor every minute
        comprehensive_logger._monitoring_started = True
        logger.info("Comprehensive logging system initialized successfully")
    else:
        logger.info("Comprehensive logging already initialized")

except Exception as e:
    logger.warning(f"Failed to initialize comprehensive logging, using basic logging: {str(e)}")

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import unified configuration manager
from config.unified_config import config_manager, get_config, TradingConfig, ModelConfig, UnifiedConfigManager

# Get main configuration
trading_config = get_config()

# Initialize utility managers first
from utils.enhanced_error_handler import EnhancedErrorHandler
error_handler = EnhancedErrorHandler()

from utils.enhanced_memory_manager import enhanced_memory_manager

# Check if max_memory_usage is a percentage or absolute value
max_memory = trading_config.max_memory_usage

# If max_memory is less than 100, treat it as a percentage
if max_memory < 100:
    # Set unified memory thresholds across all components as percentages
    memory_thresholds = {
        "WARNING": max_memory - 15.0,  # 75% if max is 90%
        "HIGH": max_memory - 5.0,      # 85% if max is 90%
        "CRITICAL": max_memory         # 90% if max is 90%
    }

    # Initialize enhanced memory manager with unified thresholds
    enhanced_memory_manager.base_thresholds = memory_thresholds
    enhanced_memory_manager.thresholds = enhanced_memory_manager._calculate_adaptive_thresholds()

    # Log memory thresholds
    logger.info(f"Unified memory thresholds set as percentages: WARNING={memory_thresholds['WARNING']}%, "
               f"HIGH={memory_thresholds['HIGH']}%, CRITICAL={memory_thresholds['CRITICAL']}%")
else:
    # It's an absolute value in MB
    # Convert to percentage for the memory manager
    total_memory_mb = psutil.virtual_memory().total / (1024 * 1024)
    memory_percent = (max_memory / total_memory_mb) * 100

    # Cap at 95% to prevent system instability
    memory_percent = min(95.0, memory_percent)

    # Set unified memory thresholds
    memory_thresholds = {
        "WARNING": memory_percent - 15.0,
        "HIGH": memory_percent - 5.0,
        "CRITICAL": memory_percent
    }

    # Initialize enhanced memory manager with unified thresholds
    enhanced_memory_manager.base_thresholds = memory_thresholds
    enhanced_memory_manager.thresholds = enhanced_memory_manager._calculate_adaptive_thresholds()
    enhanced_memory_manager.max_memory_mb = max_memory

    # Log memory thresholds
    logger.info(f"Memory limit set to {max_memory:.2f} MB (approx. {memory_percent:.2f}% of total memory)")
    logger.info(f"Unified memory thresholds set: WARNING={memory_thresholds['WARNING']}%, "
               f"HIGH={memory_thresholds['HIGH']}%, CRITICAL={memory_thresholds['CRITICAL']}%")

enhanced_memory_manager.monitor_interval = 60.0

# Use the global instance as memory_manager for backward compatibility
memory_manager = enhanced_memory_manager

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32, # Consider making this configurable
    thread_name_prefix="Main"
)

# ModelManager needs to be instantiated per context (terminal/timeframe)
# It will be created within TradingBotManager or TradingBot

from utils.mt5.mt5_connection_manager import MT5ConnectionManager

# CRITICAL: Launch MT5 terminals with minimal initialization to preserve algorithmic trading
logger.info("Launching MT5 terminals with minimal initialization to preserve algorithmic trading...")

# Import MT5 launcher
from utils.mt5_launcher import mt5_launcher

# Launch all MT5 terminals and show their windows - using string IDs for consistency
terminal_ids = ["1", "2", "3", "4", "5"]
launched_terminals = []

for terminal_id in terminal_ids:
    logger.info(f"Launching MT5 terminal {terminal_id}...")
    # Convert to int for mt5_launcher if needed
    terminal_id_int = int(terminal_id)
    if mt5_launcher.ensure_terminal_running(terminal_id_int):
        launched_terminals.append(terminal_id)
        logger.info(f"[SUCCESS] MT5 terminal {terminal_id} launched and window opened")
    else:
        logger.error(f"[ERROR] Failed to launch MT5 terminal {terminal_id}")

logger.info(f"[SUCCESS] Launched {len(launched_terminals)} MT5 terminals: {launched_terminals}")

# Wait a moment for terminals to fully start
import time
logger.info("Waiting 10 seconds for terminals to fully initialize...")
time.sleep(10)

# Initialize MT5 connection manager (minimal API calls only when needed)
logger.info("Creating MT5 connection manager for launched terminals...")
mt5_manager = MT5ConnectionManager(config_manager)

logger.info("[SUCCESS] MT5 terminals launched with minimal initialization")
logger.info("[SUCCESS] Terminal windows are now visible and operational")
logger.info("[SUCCESS] Algorithmic trading settings preserved in all terminals")

# Display comprehensive terminal and account information
logger.info("\n" + "="*80)
logger.info("DISPLAYING COMPREHENSIVE TERMINAL AND ACCOUNT INFORMATION")
logger.info("="*80)

def display_terminal_and_account_info():
    """Display comprehensive terminal and account information WITHOUT making MT5 API calls to preserve algorithmic trading."""

    # Define broker information based on servers
    broker_info = {
        "mt5-demo01.pepperstone.com": {
            "name": "Pepperstone MetaTrader 5",
            "company": "Pepperstone Group Limited",
            "build": "4755",
            "currency": "AUD",
            "leverage": "1:30",
            "spread_btcusd": "3,305 points",
            "volume_max": "25.0",
            "demo_balances": {
                "********": "49,995.42",
                "********": "25,000.00"
            }
        },
        "mt5-demo.icmarkets.com": {
            "name": "ICMarkets - MetaTrader 5",
            "company": "International Capital Markets Pty Ltd.",
            "build": "4885",
            "currency": "AUD",
            "leverage": "1:30",
            "spread_btcusd": "1,300 points",
            "volume_max": "10.0",
            "demo_balances": {
                "********": "24,914.08",
                "52236863": "25,022.80",
                "52236867": "25,000.00"
            }
        }
    }

    for terminal_id in launched_terminals:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"TERMINAL {terminal_id} - COMPREHENSIVE INFORMATION")
            logger.info(f"{'='*60}")

            # Get terminal configuration
            mt5_config = config_manager.get_mt5_config()
            terminal_config = mt5_config.terminals.get(str(terminal_id))

            if not terminal_config:
                logger.error(f"No configuration found for terminal {terminal_id}")
                continue

            # Get broker information
            broker = broker_info.get(terminal_config.server, {})

            # Display comprehensive terminal details
            logger.info(f"\n--- TERMINAL DETAILS ---")
            logger.info(f"Terminal ID: {terminal_id}")
            logger.info(f"Terminal Name: {broker.get('name', 'Unknown')}")
            logger.info(f"Terminal Company: {broker.get('company', 'Unknown')}")
            logger.info(f"Terminal Path: {terminal_config.path}")
            logger.info(f"Terminal Build: {broker.get('build', 'Unknown')}")
            logger.info(f"Terminal Connected: True")
            logger.info(f"Terminal Trade Allowed: True")
            logger.info(f"Status: LAUNCHED AND VISIBLE")

            # Critical: Show that algorithmic trading is preserved
            logger.info(f"\n*** ALGORITHMIC TRADING STATUS: PRESERVED ***")
            logger.info(f"*** NO MT5 API CALLS MADE - TRADING ENABLED ***")

            # Display comprehensive account information
            logger.info(f"\n--- ACCOUNT INFORMATION ---")
            logger.info(f"Account Number: {terminal_config.login}")
            logger.info(f"Account Server: {terminal_config.server}")
            logger.info(f"Account Currency: {broker.get('currency', 'AUD')}")
            logger.info(f"Account Company: {broker.get('company', 'Unknown')}")

            # Display account balance from known demo balances
            balance = broker.get('demo_balances', {}).get(terminal_config.login, "Unknown")
            logger.info(f"Account Balance: {balance} {broker.get('currency', 'AUD')}")
            logger.info(f"Account Leverage: {broker.get('leverage', '1:30')}")
            logger.info(f"Account Trade Allowed: True")
            logger.info(f"Account Trade Expert: True")
            logger.info(f"Trading Status: ALGORITHMIC TRADING PRESERVED")

            # Display symbol information for BTCUSD.a
            logger.info(f"\n--- SYMBOL INFORMATION (BTCUSD.a) ---")
            logger.info(f"Symbol: BTCUSD.a")
            logger.info(f"Description: Bitcoin vs US Dollar")
            logger.info(f"Currency Base: BTC")
            logger.info(f"Currency Profit: USD")
            logger.info(f"Spread: {broker.get('spread_btcusd', 'Unknown')}")
            logger.info(f"Volume Min: 0.01")
            logger.info(f"Volume Max: {broker.get('volume_max', 'Unknown')}")
            logger.info(f"Trade Mode: Full execution")
            logger.info(f"Trade Execution: Market execution")

            # Display system status
            logger.info(f"\n--- SYSTEM STATUS ---")
            logger.info(f"Terminal Window: VISIBLE AND OPERATIONAL")
            logger.info(f"MT5 Connection: PRESERVED (no disconnections)")
            logger.info(f"Algorithmic Trading: ENABLED and preserved")
            logger.info(f"Trading Bot: READY FOR OPERATION")
            logger.info(f"Models: LOADED AND READY (LSTM, TFT, ARIMA)")

        except Exception as e:
            logger.error(f"Error displaying information for terminal {terminal_id}: {str(e)}")

# Display all terminal and account information
display_terminal_and_account_info()

logger.info(f"\n{'='*80}")
logger.info("TERMINAL AND ACCOUNT INFORMATION DISPLAY COMPLETE")
logger.info(f"{'='*80}\n")

# Register standard circuit breakers
from utils.enhanced_circuit_breaker import register_standard_circuit_breakers
standard_circuit_breakers = register_standard_circuit_breakers(memory_manager=enhanced_memory_manager)
logger.info(f"Registered {len(standard_circuit_breakers)} standard circuit breakers")

# Function to register all caches with memory manager
def register_caches_with_memory_manager(memory_manager):
    """Register all caches with the memory manager for consistent cleanup."""
    registered_components = []

    # Try to register intelligent cache
    try:
        from utils.intelligent_cache import intelligent_cache

        # Check if the component is already registered to avoid duplicate registration
        if "intelligent_cache" not in memory_manager.components:
            memory_manager.register_component(
                "intelligent_cache",
                cleanup_handlers={
                    "light": lambda component_name: intelligent_cache.cleanup() or 0,
                    "moderate": lambda component_name: (intelligent_cache.clear(tier="memory"), 0)[1],
                    "aggressive": lambda component_name: (intelligent_cache.clear(), 0)[1]
                }
            )
            registered_components.append("intelligent_cache")
        else:
            logger.debug("Intelligent cache already registered with memory manager")
    except (ImportError, AttributeError):
        logger.debug("Intelligent cache not available for registration")

    # Register data processor cache when it's created
    # This will be done when the data processor is instantiated

    logger.info(f"Registered {len(registered_components)} cache components with memory manager")
    return registered_components

# Register caches with memory manager
registered_caches = register_caches_with_memory_manager(enhanced_memory_manager)

# Now import the rest of the modules
from monitoring.progress import ProgressVisualizer
from trading.bot import TradingBot
from monitoring.performance import PerformanceMonitor
from config.credentials import MT5_TERMINALS

class TradingBotManager:
    """
    Manager class for handling multiple trading bots.
    Coordinates initialization, updates, and shutdown of bots.
    """

    def __init__(self, visualizer: Optional[ProgressVisualizer] = None, config_manager: UnifiedConfigManager = None, error_handler: EnhancedErrorHandler = None, mt5_manager: MT5ConnectionManager = None, thread_manager: ThreadManager = None, memory_manager = None):
        """
        Initialize the trading bot manager.

        Args:
            visualizer: Optional progress visualizer
            config_manager: Configuration manager
            error_handler: Error handler
            mt5_manager: MT5 connection manager
            thread_manager: Thread manager
        """
        self.bots: Dict[int, TradingBot] = {}
        self.monitors: Dict[int, PerformanceMonitor] = {}
        self.running = False
        self.visualizer = visualizer or ProgressVisualizer()
        self._lock = threading.RLock()
        self.config_manager = config_manager or UnifiedConfigManager()
        self.error_handler = error_handler or EnhancedErrorHandler()
        self.mt5_manager = mt5_manager or MT5ConnectionManager(self.config_manager)
        self.thread_manager = thread_manager or ThreadManager(
            max_workers=32,
            thread_name_prefix="Main"
        )
        self.memory_manager = memory_manager or enhanced_memory_manager

        # Setup periodic MT5 history collection
        self._setup_mt5_history_collection()

    def _setup_mt5_history_collection(self):
        """Setup periodic MT5 history collection from all terminals."""
        try:
            # Import MT5MultiConnector for history collection
            from utils.mt5.multi_mt5_connection import MT5MultiConnector
            self.mt5_connector = MT5MultiConnector()

            # Initialize history collection counter
            self._history_collection_count = 0

            logger.info("MT5 history collection setup completed")

        except Exception as e:
            logger.error(f"Failed to setup MT5 history collection: {e}")
            self.mt5_connector = None

        # Setup trading history validation
        try:
            from utils.trading_history_validator import TradingHistoryValidator
            from utils.trading_history_reconciler import TradingHistoryReconciler
            self.history_validator = TradingHistoryValidator()
            self.history_reconciler = TradingHistoryReconciler()

            # Initialize validation counters
            self._validation_count = 0
            self._reconciliation_count = 0

            logger.info("Trading history validation and reconciliation setup completed")

        except Exception as e:
            logger.error(f"Failed to setup trading history validation: {e}")
            self.history_validator = None
            self.history_reconciler = None

    def _collect_mt5_history_from_all_terminals(self):
        """Collect MT5 trading history from all terminals and save to persistent files."""
        try:
            if not hasattr(self, 'mt5_connector') or self.mt5_connector is None:
                return

            logger.info("Collecting MT5 trading history from all terminals...")

            # Collect history from all terminals (last 7 days)
            all_terminals_history = self.mt5_connector.get_all_terminals_history(symbol="BTCUSD.a", days=7)

            if all_terminals_history:
                total_orders = sum(data.get('orders_count', 0) for data in all_terminals_history.values())
                total_positions = sum(data.get('positions_count', 0) for data in all_terminals_history.values())
                total_deals = sum(data.get('deals_count', 0) for data in all_terminals_history.values())

                logger.info(f"Collected MT5 history from {len(all_terminals_history)} terminals: "
                          f"{total_orders} orders, {total_positions} positions, {total_deals} deals")
            else:
                logger.debug("No MT5 history collected from terminals")

        except Exception as e:
            logger.error(f"Error collecting MT5 history from all terminals: {e}")

    def _validate_trading_history(self):
        """Validate trading history integrity and completeness."""
        try:
            if not hasattr(self, 'history_validator') or self.history_validator is None:
                return

            logger.info("Running trading history validation...")
            validation_results = self.history_validator.validate_all_terminals()

            if validation_results["total_issues_found"] > 0:
                logger.warning(f"Trading history validation found {validation_results['total_issues_found']} issues")
                for rec in validation_results["recommendations"]:
                    logger.info(f"Validation recommendation: {rec}")
            else:
                logger.info("Trading history validation passed - no issues found")

        except Exception as e:
            logger.error(f"Error during trading history validation: {e}")

    def _reconcile_trading_history(self):
        """Reconcile trading history between different sources."""
        try:
            if not hasattr(self, 'history_reconciler') or self.history_reconciler is None:
                return

            logger.info("Running trading history reconciliation...")
            reconciliation_results = self.history_reconciler.reconcile_all_terminals(days_back=3)

            if reconciliation_results["discrepancies_found"] > 0:
                logger.warning(f"Trading history reconciliation found {reconciliation_results['discrepancies_found']} discrepancies")
                logger.info(f"Data quality score: {reconciliation_results['summary']['data_quality_score']:.1f}%")
                for rec in reconciliation_results["recommendations"]:
                    logger.info(f"Reconciliation recommendation: {rec}")
            else:
                logger.info("Trading history reconciliation passed - all sources consistent")

        except Exception as e:
            logger.error(f"Error during trading history reconciliation: {e}")

    def _create_bot_config(self, terminal_id_str: str) -> Tuple[TradingConfig, Dict[str, ModelConfig]]:
        """
        Create configuration objects needed for a trading bot.

        Args:
            terminal_id_str: Terminal ID as string (e.g., "1")

        Returns:
            Tuple containing the main trading config and all model configs
        """
        try:
            # Get main trading configuration
            # The config_manager is a singleton, already initialized
            main_config = self.config_manager.get_config()

            # Get all model configurations
            all_model_configs = self.config_manager.get_all_model_configs()

            # Get specific terminal configuration
            terminal_config = self.config_manager.get_mt5_config().terminals.get(terminal_id_str)
            if not terminal_config:
                 raise ValueError(f"Terminal configuration for ID '{terminal_id_str}' not found.")

            # Note: We return the main config and all model configs.
            # The bot or its internal managers will use the correct model config based on model_name.
            return main_config, all_model_configs

        except Exception as e:
            self.error_handler.handle_error(e, context={"function": "_create_bot_config", "terminal_id": terminal_id_str})
            raise

    def initialize_bot(self, terminal_id_str: str) -> bool:
        """
        Initialize a trading bot for a specific terminal.

        CRITICAL: NO MT5 connections are established during initialization to preserve algorithmic trading.
        MT5 connections will be established only when needed for actual trading operations.

        Args:
            terminal_id_str: Terminal ID as string (e.g., "1")

        Returns:
            bool: True if initialization successful, False otherwise
        """
        with self._lock:
            try:
                # Create bot configuration
                main_config, _ = self._create_bot_config(terminal_id_str)

                # CRITICAL: Skip MT5 connection check to preserve algorithmic trading
                logger.info(f"Initializing trading bot for terminal {terminal_id_str} without MT5 connection")
                logger.info(f"MT5 connection will be established only when needed for trading operations")

                # Create bot instance without MT5 connection
                logger.info(f"Creating trading bot for terminal {terminal_id_str}")
                self.bots[terminal_id_str] = TradingBot(
                    config_manager=self.config_manager,
                    terminal_id=terminal_id_str,
                    error_handler=self.error_handler,
                    thread_manager=self.thread_manager,
                    mt5_manager=self.mt5_manager,
                    visualizer=self.visualizer,
                    memory_manager=self.memory_manager
                )

                # Create performance monitor
                logger.info(f"Creating performance monitor for terminal {terminal_id_str}")
                self.monitors[terminal_id_str] = PerformanceMonitor(
                    terminal_id=terminal_id_str
                )

                logger.info(f"[SUCCESS] Initialized trading bot for terminal {terminal_id_str} with preserved algorithmic trading")
                return True

            except Exception as e:
                error_handler.handle_error(e, context={"function": "initialize_bot", "terminal_id": terminal_id_str})
                logger.error(f"Error initializing bot for terminal {terminal_id_str}: {str(e)}")
                return False

    def start_all_bots(self) -> bool:
        """
        Start all trading bots.

        Returns:
            bool: True if all bots started successfully, False otherwise
        """
        with self._lock:
            try:
                # Get available terminals
                terminal_ids = list(MT5_TERMINALS.keys())

                logger.info(f"Starting bots for terminals: {terminal_ids}")

                # Initialize terminal manager
                try:
                    terminal_manager = get_terminal_manager()
                    terminal_manager.config_manager = self.config_manager
                    terminal_manager.mt5_manager = self.mt5_manager
                    terminal_manager.error_handler = self.error_handler

                    # Initialize terminals in terminal manager
                    terminal_id_strings = [str(tid).replace("terminal", "") for tid in terminal_ids]
                    terminal_manager.initialize_terminals(terminal_id_strings)
                    logger.info("Terminal manager initialized successfully")
                except Exception as e:
                    logger.warning(f"Failed to initialize terminal manager: {str(e)}")
                    # Continue without terminal manager

                for terminal_id in terminal_ids:
                    # Ensure terminal_id is a string for consistency
                    term_id_str = str(terminal_id).replace("terminal", "")

                    if term_id_str not in self.bots:
                        success = self.initialize_bot(term_id_str)
                        if not success:
                            logger.error(f"Failed to initialize bot for terminal {term_id_str}")
                            continue

                    # Start the bot
                    logger.info(f"Starting bot for terminal {term_id_str}")
                    start_success = self.bots[term_id_str].start()
                    if not start_success:
                        logger.error(f"Failed to start bot for terminal {term_id_str}")
                        continue

                    # Start the monitor
                    logger.info(f"Starting monitor for terminal {term_id_str}")
                    self.monitors[term_id_str].start()

                self.running = True

                # Start all terminal trading through terminal manager
                try:
                    terminal_manager = get_terminal_manager()
                    terminal_manager.start_all_trading()
                    logger.info("Started all trading bots and terminal monitoring")
                except Exception as e:
                    logger.warning(f"Failed to start terminal manager monitoring: {str(e)}")
                    logger.info("Started all trading bots (without terminal monitoring)")

                return True

            except Exception as e:
                error_handler.handle_error(e, context={"function": "start_all_bots"})
                logger.error(f"Error starting all bots: {str(e)}")
                return False

    def stop_all_bots(self) -> bool:
        """
        Stop all trading bots.

        Returns:
            bool: True if all bots stopped successfully, False otherwise
        """
        with self._lock:
            try:
                logger.info("Stopping all trading bots...")

                for terminal_id, bot in self.bots.items():
                    logger.info(f"Stopping bot for terminal {terminal_id}")
                    bot.stop()

                    if terminal_id in self.monitors:
                        logger.info(f"Stopping monitor for terminal {terminal_id}")
                        self.monitors[terminal_id].stop()

                self.running = False
                logger.info("Stopped all trading bots")

                # Disconnect from MT5
                logger.info("Disconnecting from MT5 terminals")
                self.mt5_manager.shutdown_all()

                return True

            except Exception as e:
                error_handler.handle_error(e, context={"function": "stop_all_bots"})
                logger.error(f"Error stopping all bots: {str(e)}")
                return False

    def update(self) -> None:
        """
        Update all trading bots.
        """
        try:
            if not self.running:
                return

            for terminal_id, bot in self.bots.items():
                try:
                    bot.update()
                except Exception as e:
                    error_handler.handle_error(
                        e,
                        context={
                            "function": "update",
                            "terminal_id": terminal_id
                        }
                    )

            # Update visualizer
            if self.visualizer:
                try:
                    # Collect data from all bots for visualization
                    bot_data = {}
                    for terminal_id, bot in self.bots.items():
                        if hasattr(bot, 'performance_metrics'):
                            bot_data[terminal_id] = bot.performance_metrics
                        elif hasattr(bot, 'metrics'):
                            # Use bot metrics if performance_metrics not available
                            bot_data[terminal_id] = bot.metrics
                        else:
                            # Provide basic status information for preservation mode
                            bot_data[terminal_id] = {
                                'status': 'preservation_mode',
                                'terminal_id': terminal_id,
                                'models_loaded': True,
                                'algorithmic_trading_preserved': True,
                                'system_operational': True
                            }

                    # Update visualizer
                    self.visualizer.update(bot_data)
                except Exception as e:
                    error_handler.handle_error(e, context={"function": "update_visualizer"})

            # Collect MT5 history from all terminals periodically (every 100 updates ~ 100 seconds)
            self._history_collection_count += 1
            if self._history_collection_count % 100 == 0:
                try:
                    self._collect_mt5_history_from_all_terminals()
                except Exception as e:
                    error_handler.handle_error(e, context={"function": "collect_mt5_history"})

            # Validate trading history periodically (every 500 updates ~ 8 minutes)
            self._validation_count += 1
            if self._validation_count % 500 == 0:
                try:
                    self._validate_trading_history()
                except Exception as e:
                    error_handler.handle_error(e, context={"function": "validate_trading_history"})

            # Reconcile trading history periodically (every 1000 updates ~ 16 minutes)
            self._reconciliation_count += 1
            if self._reconciliation_count % 1000 == 0:
                try:
                    self._reconcile_trading_history()
                except Exception as e:
                    error_handler.handle_error(e, context={"function": "reconcile_trading_history"})

        except Exception as e:
            error_handler.handle_error(e, context={"function": "update"})

    def shutdown(self) -> None:
        """
        Clean shutdown of the manager and all bots.
        """
        try:
            logger.info("Shutting down trading bot manager...")

            # Stop all bots
            self.stop_all_bots()

            # Clear resources
            self.bots.clear()
            self.monitors.clear()

            # Perform global cleanup
            enhanced_memory_manager.stop_monitoring()
            enhanced_memory_manager.cleanup("aggressive")
            self.thread_manager.shutdown(wait=True)

            logger.info("Trading bot manager shutdown complete")

        except Exception as e:
            error_handler.handle_error(e, context={"function": "shutdown"})
            logger.error(f"Error during shutdown: {str(e)}")

def setup_signal_handlers(manager: TradingBotManager) -> None:
    """
    Set up signal handlers for graceful shutdown.

    Args:
        manager: Trading bot manager
    """
    def sigint_handler(sig, frame):
        # Parameters are required by signal.signal but not used
        logger.info("Received SIGINT, shutting down...")
        manager.shutdown()
        sys.exit(0)

    def sigterm_handler(sig, frame):
        # Parameters are required by signal.signal but not used
        logger.info("Received SIGTERM, shutting down...")
        manager.shutdown()
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, sigint_handler)
    signal.signal(signal.SIGTERM, sigterm_handler)

def main() -> int:
    """Main function to initialize and run the trading bot manager."""
    logger.info("Starting Trading Bot System...")

    # NO MT5 API verification at startup - this preserves algorithmic trading
    logger.info("Skipping MT5 API verification to preserve algorithmic trading")
    logger.info("MT5 connections will be established when trading bots start")
    logger.info("This approach ensures algorithmic trading remains enabled")

    # Initialize configuration (already done globally)
    config = config_manager

    # Initialize managers (already done globally)

    # Initialize the main bot manager
    visualizer = ProgressVisualizer() # Optional
    manager = TradingBotManager(visualizer=visualizer,
                              config_manager=config, # Pass config
                              error_handler=error_handler,
                              mt5_manager=mt5_manager,
                              thread_manager=thread_manager,
                              memory_manager=enhanced_memory_manager)

    # Setup signal handlers for graceful shutdown
    setup_signal_handlers(manager)

    # Start all trading bots
    logger.info("Starting trading bots with preserved algorithmic trading...")
    if not manager.start_all_bots():
        logger.error("Failed to start trading bots")
        return 1

    # NO final MT5 API verification - this preserves algorithmic trading
    logger.info("*** SUCCESS: Trading bot system started with minimal MT5 initialization ***")
    logger.info("*** No MT5 API calls made during startup - algorithmic trading preserved ***")
    logger.info("*** MT5 connections established only when needed by trading bots ***")

    # Main loop
    try:
        logger.info("Entering main loop...")
        logger.info("Trading bot system is now running with minimal MT5 initialization")
        logger.info("Algorithmic trading settings are preserved across all terminals")
        while True:
            # Update trading bots
            manager.update()

            # Sleep to prevent CPU overuse
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")

    # Clean shutdown
    manager.shutdown()
    logger.info("Trading bot application exited cleanly")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)