@echo off
echo ===================================================
echo Unified Model Training Launcher
echo ===================================================
echo This script provides a unified interface for training models
echo using either TensorFlow or PyTorch.
echo.

REM Default values
set FRAMEWORK=tensorflow
set TIMEFRAMES=M5
set MODELS=all
set TERMINAL_ID=1
set FORCE_CPU=0
set DATA_DIR=data/historical/btcusd.a
set SYMBOL=BTCUSD.a

REM Parse command line arguments
:parse_args
if "%~1"=="" goto validate_args
if /i "%~1"=="--framework" (
    set FRAMEWORK=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--timeframes" (
    set TIMEFRAMES=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--models" (
    set MODELS=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--terminal_id" (
    set TERMINAL_ID=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--data_dir" (
    set DATA_DIR=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--symbol" (
    set SYMBOL=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--force-cpu" (
    set FORCE_CPU=1
    shift
    goto parse_args
)
if /i "%~1"=="--help" (
    goto show_help
)
echo Unknown option: %~1
goto show_help

:validate_args
REM Validate framework
if /i not "%FRAMEWORK%"=="tensorflow" if /i not "%FRAMEWORK%"=="pytorch" (
    echo Invalid framework: %FRAMEWORK%
    echo Valid options are: tensorflow, pytorch
    goto show_help
)

REM Show selected options
echo Selected options:
echo   Framework: %FRAMEWORK%
echo   Timeframes: %TIMEFRAMES%
echo   Models: %MODELS%
echo   Terminal ID: %TERMINAL_ID%
echo   Data Directory: %DATA_DIR%
echo   Symbol: %SYMBOL%
if %FORCE_CPU%==1 (
    echo   Force CPU: Yes
) else (
    echo   Force CPU: No
)
echo.

REM Check if Docker should be used
choice /C YN /M "Do you want to use Docker for training"
if errorlevel 2 goto native_training
if errorlevel 1 goto docker_training

:native_training
echo.
echo Running training with native %FRAMEWORK%...
echo.

REM Set environment variables if needed
if %FORCE_CPU%==1 (
    echo Setting CUDA_VISIBLE_DEVICES=-1 to force CPU usage
    set CUDA_VISIBLE_DEVICES=-1
)

REM Run the appropriate training script
if /i "%FRAMEWORK%"=="tensorflow" (
    echo Running TensorFlow training script...
    if %FORCE_CPU%==1 (
        python train_with_tensorflow.py --timeframes %TIMEFRAMES% --models %MODELS% --terminal_id %TERMINAL_ID% --data_dir %DATA_DIR% --symbol %SYMBOL% --force-cpu
    ) else (
        python train_with_tensorflow.py --timeframes %TIMEFRAMES% --models %MODELS% --terminal_id %TERMINAL_ID% --data_dir %DATA_DIR% --symbol %SYMBOL%
    )
) else (
    echo Running PyTorch training script...
    REM Convert comma-separated lists to space-separated for PyTorch script
    set TIMEFRAMES_SPACE=%TIMEFRAMES:,= %
    set MODELS_SPACE=%MODELS:,= %

    if %FORCE_CPU%==1 (
        python train_with_pytorch.py --timeframes %TIMEFRAMES_SPACE% --models %MODELS_SPACE% --terminal_id %TERMINAL_ID% --data_dir %DATA_DIR% --symbol %SYMBOL% --force-cpu
    ) else (
        python train_with_pytorch.py --timeframes %TIMEFRAMES_SPACE% --models %MODELS_SPACE% --terminal_id %TERMINAL_ID% --data_dir %DATA_DIR% --symbol %SYMBOL%
    )
)

goto end

:docker_training
echo.
echo Running training with Docker (%FRAMEWORK%)...
echo.

REM Check if Docker is installed
where docker >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker is not installed or not in PATH.
    echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop/
    goto end
)

REM Get current directory for mounting
set CURRENT_DIR=%CD%

REM Check if the appropriate Docker image exists
if /i "%FRAMEWORK%"=="tensorflow" (
    set DOCKER_IMAGE=tensorflow/tensorflow:latest-gpu
) else (
    set DOCKER_IMAGE=pytorch/pytorch:latest
)

docker image inspect %DOCKER_IMAGE% >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo %DOCKER_IMAGE% image not found. Pulling the latest version...
    docker pull %DOCKER_IMAGE%
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to pull Docker image. Please check your internet connection.
        goto end
    )
)

REM Run the Docker container with GPU support
echo Running Docker container with GPU support...
echo.

REM Use existing training scripts instead of non-existent ones
echo Training models using available scripts...
echo.

REM Train LSTM models
if /i "%MODELS%"=="all" (
    echo Training LSTM models...
    python train_lstm_btcusd.py
) else (
    echo %MODELS% | findstr /i "lstm" >nul
    if not errorlevel 1 (
        echo Training LSTM models...
        python train_lstm_btcusd.py
    )
)

REM Train ARIMA models
if /i "%MODELS%"=="all" (
    echo Training ARIMA models...
    call train_all_arima_models.bat
) else (
    echo %MODELS% | findstr /i "arima" >nul
    if not errorlevel 1 (
        echo Training ARIMA models...
        call train_all_arima_models.bat
    )
)

REM Train TFT models
if /i "%MODELS%"=="all" (
    echo Training TFT models...
    if exist train_all_tft_models.sh (
        bash train_all_tft_models.sh
    ) else (
        echo Warning: train_all_tft_models.sh not found, running TFT training directly
        python train_tft_pytorch.py
    )
) else (
    echo %MODELS% | findstr /i "tft" >nul
    if not errorlevel 1 (
        echo Training TFT models...
        if exist train_all_tft_models.sh (
            bash train_all_tft_models.sh
        ) else (
            echo Warning: train_all_tft_models.sh not found, running TFT training directly
            python train_tft_pytorch.py
        )
    )
)

goto end

:show_help
echo.
echo Usage: train_models.bat [options]
echo.
echo Options:
echo   --framework FRAMEWORK   Framework to use (tensorflow, pytorch) [default: tensorflow]
echo   --timeframes TIMEFRAMES Comma-separated list of timeframes (M5,M15,M30,H1,H4) or "all" [default: M5]
echo   --models MODELS         Comma-separated list of models (lstm,gru,xgboost,lightgbm,tft) or "all" [default: all]
echo   --terminal_id ID        MT5 terminal ID [default: 1]
echo   --data_dir DIR          Directory containing the data files [default: data/historical/btcusd.a]
echo   --symbol SYMBOL         Trading symbol [default: BTCUSD.a]
echo   --force-cpu             Force using CPU even if GPU is available
echo   --help                  Show this help message
echo.
echo Examples:
echo   train_models.bat --framework tensorflow --timeframes M5,M15 --models lstm,gru
echo   train_models.bat --framework pytorch --timeframes all --models all
echo   train_models.bat --framework tensorflow --force-cpu
echo.

:end
pause
