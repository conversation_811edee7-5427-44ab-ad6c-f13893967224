#!/usr/bin/env python3
"""
Trading History Reconciler

This module reconciles trading history between different sources:
- Bot trading history logs
- MT5 native history
- TradeExecutor logs
- SignalGenerator logs
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import pandas as pd

# Use comprehensive logging system
try:
    from utils.comprehensive_logging import get_main_logger, log_trading_history
    logger = get_main_logger()
except ImportError:
    # Fallback to basic logging if comprehensive logging not available
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class TradingHistoryReconciler:
    """Reconciles trading history between different sources."""
    
    def __init__(self, trading_history_base_dir: str = "logs/trading_history"):
        self.base_dir = Path(trading_history_base_dir)
        self.reconciliation_results = {}
        
    def reconcile_all_terminals(self, days_back: int = 7) -> Dict[str, Any]:
        """Reconcile trading history for all terminals."""
        logger.info(f"Starting trading history reconciliation for last {days_back} days...")
        
        overall_results = {
            "reconciliation_timestamp": datetime.now().isoformat(),
            "days_analyzed": days_back,
            "terminals_reconciled": 0,
            "discrepancies_found": 0,
            "terminal_results": {},
            "summary": {},
            "recommendations": []
        }
        
        # Reconcile each terminal
        for terminal_id in range(1, 6):
            terminal_results = self.reconcile_terminal_history(terminal_id, days_back)
            overall_results["terminal_results"][f"terminal_{terminal_id}"] = terminal_results
            overall_results["terminals_reconciled"] += 1
            
            if terminal_results["discrepancies_found"] > 0:
                overall_results["discrepancies_found"] += terminal_results["discrepancies_found"]
        
        # Generate summary
        overall_results["summary"] = self._generate_summary(overall_results)
        
        # Generate recommendations
        overall_results["recommendations"] = self._generate_reconciliation_recommendations(overall_results)
        
        # Save reconciliation report
        self._save_reconciliation_report(overall_results)
        
        logger.info(f"Trading history reconciliation completed. Found {overall_results['discrepancies_found']} discrepancies.")
        
        return overall_results
    
    def reconcile_terminal_history(self, terminal_id: int, days_back: int = 7) -> Dict[str, Any]:
        """Reconcile trading history for a specific terminal."""
        terminal_dir = self.base_dir / f"terminal_{terminal_id}"
        
        results = {
            "terminal_id": terminal_id,
            "reconciliation_timestamp": datetime.now().isoformat(),
            "days_analyzed": days_back,
            "sources_analyzed": {},
            "discrepancies_found": 0,
            "discrepancy_details": [],
            "data_completeness": {},
            "recommendations": []
        }
        
        if not terminal_dir.exists():
            results["discrepancies_found"] += 1
            results["discrepancy_details"].append(f"Terminal {terminal_id} directory does not exist")
            return results
        
        # Load data from different sources
        sources_data = self._load_terminal_data_sources(terminal_dir, days_back)
        results["sources_analyzed"] = {k: len(v) for k, v in sources_data.items()}
        
        # Reconcile trades between sources
        trade_reconciliation = self._reconcile_trades(sources_data)
        results["discrepancies_found"] += trade_reconciliation["discrepancies_count"]
        results["discrepancy_details"].extend(trade_reconciliation["discrepancies"])
        
        # Reconcile signals
        signal_reconciliation = self._reconcile_signals(sources_data)
        results["discrepancies_found"] += signal_reconciliation["discrepancies_count"]
        results["discrepancy_details"].extend(signal_reconciliation["discrepancies"])
        
        # Check data completeness
        results["data_completeness"] = self._check_data_completeness(sources_data, days_back)
        
        # Generate terminal-specific recommendations
        results["recommendations"] = self._generate_terminal_recommendations(results)
        
        return results
    
    def _load_terminal_data_sources(self, terminal_dir: Path, days_back: int) -> Dict[str, List[Dict]]:
        """Load data from all available sources for a terminal."""
        cutoff_date = datetime.now() - timedelta(days=days_back)
        sources_data = {
            "bot_trades": [],
            "executor_trades": [],
            "bot_signals": [],
            "generator_signals": [],
            "mt5_history": [],
            "performance_data": []
        }
        
        # Load bot trades
        bot_trades_files = list((terminal_dir / "trades").glob("bot_trades_*.json"))
        for file_path in bot_trades_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        sources_data["bot_trades"].extend([
                            record for record in data 
                            if self._is_within_timeframe(record.get("timestamp"), cutoff_date)
                        ])
            except Exception as e:
                logger.warning(f"Failed to load bot trades from {file_path}: {e}")
        
        # Load executor trades
        executor_trades_files = list((terminal_dir / "trades").glob("executor_trades_*.json"))
        for file_path in executor_trades_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        sources_data["executor_trades"].extend([
                            record for record in data 
                            if self._is_within_timeframe(record.get("timestamp"), cutoff_date)
                        ])
            except Exception as e:
                logger.warning(f"Failed to load executor trades from {file_path}: {e}")
        
        # Load signals
        signal_files = list((terminal_dir / "signals").glob("*.json"))
        for file_path in signal_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        if "bot_signals" in file_path.name:
                            sources_data["bot_signals"].extend([
                                record for record in data 
                                if self._is_within_timeframe(record.get("timestamp"), cutoff_date)
                            ])
                        elif "signals" in file_path.name:
                            sources_data["generator_signals"].extend([
                                record for record in data 
                                if self._is_within_timeframe(record.get("timestamp"), cutoff_date)
                            ])
            except Exception as e:
                logger.warning(f"Failed to load signals from {file_path}: {e}")
        
        # Load MT5 history
        mt5_files = list((terminal_dir / "mt5_history").glob("*.json"))
        for file_path in mt5_files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, dict) and "history_orders" in data:
                        sources_data["mt5_history"].extend([
                            record for record in data["history_orders"] 
                            if self._is_within_timeframe(record.get("open_time"), cutoff_date)
                        ])
            except Exception as e:
                logger.warning(f"Failed to load MT5 history from {file_path}: {e}")
        
        return sources_data
    
    def _is_within_timeframe(self, timestamp_str: Optional[str], cutoff_date: datetime) -> bool:
        """Check if timestamp is within the analysis timeframe."""
        if not timestamp_str:
            return False
        
        try:
            if isinstance(timestamp_str, str):
                # Try different timestamp formats
                for fmt in ["%Y-%m-%dT%H:%M:%S.%f", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S"]:
                    try:
                        timestamp = datetime.strptime(timestamp_str, fmt)
                        return timestamp >= cutoff_date
                    except ValueError:
                        continue
            return False
        except Exception:
            return False
    
    def _reconcile_trades(self, sources_data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """Reconcile trades between different sources."""
        reconciliation = {
            "discrepancies_count": 0,
            "discrepancies": [],
            "bot_trades_count": len(sources_data["bot_trades"]),
            "executor_trades_count": len(sources_data["executor_trades"]),
            "mt5_trades_count": len(sources_data["mt5_history"]),
            "matched_trades": 0,
            "unmatched_bot_trades": 0,
            "unmatched_executor_trades": 0,
            "unmatched_mt5_trades": 0
        }
        
        # Extract trade tickets for comparison
        bot_tickets = set()
        executor_tickets = set()
        mt5_tickets = set()
        
        for trade in sources_data["bot_trades"]:
            ticket = trade.get("ticket")
            if ticket and ticket != "unknown":
                bot_tickets.add(str(ticket))
        
        for trade in sources_data["executor_trades"]:
            ticket = trade.get("ticket")
            if ticket and ticket != "unknown":
                executor_tickets.add(str(ticket))
        
        for trade in sources_data["mt5_history"]:
            ticket = trade.get("ticket")
            if ticket:
                mt5_tickets.add(str(ticket))
        
        # Find matches and discrepancies
        all_tickets = bot_tickets | executor_tickets | mt5_tickets
        
        for ticket in all_tickets:
            in_bot = ticket in bot_tickets
            in_executor = ticket in executor_tickets
            in_mt5 = ticket in mt5_tickets
            
            if in_bot and in_executor and in_mt5:
                reconciliation["matched_trades"] += 1
            else:
                reconciliation["discrepancies_count"] += 1
                missing_sources = []
                if not in_bot:
                    missing_sources.append("bot_trades")
                    reconciliation["unmatched_bot_trades"] += 1
                if not in_executor:
                    missing_sources.append("executor_trades")
                    reconciliation["unmatched_executor_trades"] += 1
                if not in_mt5:
                    missing_sources.append("mt5_history")
                    reconciliation["unmatched_mt5_trades"] += 1
                
                reconciliation["discrepancies"].append(
                    f"Trade ticket {ticket} missing from: {', '.join(missing_sources)}"
                )
        
        return reconciliation
    
    def _reconcile_signals(self, sources_data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """Reconcile signals between different sources."""
        reconciliation = {
            "discrepancies_count": 0,
            "discrepancies": [],
            "bot_signals_count": len(sources_data["bot_signals"]),
            "generator_signals_count": len(sources_data["generator_signals"]),
            "signal_consistency_issues": []
        }
        
        # Check for significant differences in signal counts
        bot_count = reconciliation["bot_signals_count"]
        generator_count = reconciliation["generator_signals_count"]
        
        if abs(bot_count - generator_count) > max(bot_count, generator_count) * 0.1:  # 10% threshold
            reconciliation["discrepancies_count"] += 1
            reconciliation["discrepancies"].append(
                f"Significant difference in signal counts: bot={bot_count}, generator={generator_count}"
            )
        
        return reconciliation
    
    def _check_data_completeness(self, sources_data: Dict[str, List[Dict]], days_back: int) -> Dict[str, Any]:
        """Check data completeness across the analysis period."""
        completeness = {
            "expected_days": days_back,
            "days_with_data": {},
            "missing_days": {},
            "completeness_score": 0.0
        }
        
        # This is a simplified completeness check
        # In a full implementation, you would check for data gaps by day/hour
        
        for source, data in sources_data.items():
            if data:
                completeness["days_with_data"][source] = "Has data"
            else:
                completeness["missing_days"][source] = "No data found"
        
        # Calculate simple completeness score
        sources_with_data = len(completeness["days_with_data"])
        total_sources = len(sources_data)
        completeness["completeness_score"] = (sources_with_data / total_sources) * 100 if total_sources > 0 else 0
        
        return completeness
    
    def _generate_summary(self, overall_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of reconciliation results."""
        summary = {
            "total_discrepancies": overall_results["discrepancies_found"],
            "terminals_with_issues": 0,
            "most_common_issues": [],
            "data_quality_score": 0.0
        }
        
        # Count terminals with issues
        for terminal_result in overall_results["terminal_results"].values():
            if terminal_result["discrepancies_found"] > 0:
                summary["terminals_with_issues"] += 1
        
        # Calculate data quality score
        total_terminals = overall_results["terminals_reconciled"]
        clean_terminals = total_terminals - summary["terminals_with_issues"]
        summary["data_quality_score"] = (clean_terminals / total_terminals) * 100 if total_terminals > 0 else 0
        
        return summary
    
    def _generate_reconciliation_recommendations(self, overall_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on reconciliation results."""
        recommendations = []
        
        if overall_results["discrepancies_found"] == 0:
            recommendations.append("✓ All trading history sources are consistent")
        else:
            recommendations.append(f"⚠ Found {overall_results['discrepancies_found']} discrepancies across sources")
            recommendations.append("• Review individual terminal reconciliation results")
            recommendations.append("• Check trading bot and MT5 connection stability")
            recommendations.append("• Verify logging configurations are consistent")
        
        return recommendations
    
    def _generate_terminal_recommendations(self, terminal_results: Dict[str, Any]) -> List[str]:
        """Generate terminal-specific recommendations."""
        recommendations = []
        
        if terminal_results["discrepancies_found"] == 0:
            recommendations.append("✓ All sources consistent for this terminal")
        else:
            recommendations.append(f"⚠ Found {terminal_results['discrepancies_found']} discrepancies")
            recommendations.append("• Check MT5 connection stability")
            recommendations.append("• Verify trading bot is logging properly")
        
        return recommendations
    
    def _save_reconciliation_report(self, results: Dict[str, Any]) -> None:
        """Save reconciliation report to file."""
        try:
            reports_dir = self.base_dir / "reconciliation_reports"
            reports_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = reports_dir / f"trading_history_reconciliation_{timestamp}.json"
            
            with open(report_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Trading history reconciliation report saved to: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save reconciliation report: {e}")

def reconcile_trading_history(days_back: int = 7) -> Dict[str, Any]:
    """Convenience function to reconcile all trading history."""
    reconciler = TradingHistoryReconciler()
    return reconciler.reconcile_all_terminals(days_back)

if __name__ == "__main__":
    # Run reconciliation when script is executed directly
    import argparse
    
    parser = argparse.ArgumentParser(description="Reconcile trading history between sources")
    parser.add_argument("--days", type=int, default=7, help="Number of days to analyze (default: 7)")
    args = parser.parse_args()
    
    results = reconcile_trading_history(args.days)
    
    print("\n" + "="*60)
    print("TRADING HISTORY RECONCILIATION RESULTS")
    print("="*60)
    print(f"Days analyzed: {results['days_analyzed']}")
    print(f"Terminals reconciled: {results['terminals_reconciled']}")
    print(f"Discrepancies found: {results['discrepancies_found']}")
    print(f"Data quality score: {results['summary']['data_quality_score']:.1f}%")
    print("\nRecommendations:")
    for rec in results['recommendations']:
        print(f"  {rec}")
    print("="*60)
