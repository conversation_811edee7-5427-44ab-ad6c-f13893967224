import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from dataclasses import dataclass
import gc
import psutil
import time
import os

logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """Statistics about the data preprocessing operations"""
    input_rows: int
    output_rows: int
    input_columns: int
    output_columns: int
    missing_value_count: int
    outliers_removed: int
    memory_usage_mb: float
    processing_time_ms: float

class DataPreprocessor:
    """
    Handles data preprocessing for the trading bot including:

    1. Missing value handling
    2. Outlier detection and removal
    3. Feature engineering with technical indicators
    4. Normalization and scaling
    5. Sequence preparation for ML models
    6. Memory optimization
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the data preprocessor

        Args:
            config: Configuration dictionary containing necessary parameters.
                    Expected keys include strategy settings (e.g., sequence_length,
                    batch_size, feature usage flags, outlier_std_threshold) and
                    preprocessor settings (e.g., preprocessor_max_batch_size).
        """
        self.config = config # Store the passed config dictionary
        self.logger = logging.getLogger(__name__)

        # Get parameters from the config dictionary with defaults
        # Nested dictionaries (like strategy) are accessed carefully
        strategy_config = self.config.get('strategy', {})
        self.sequence_length = strategy_config.get('sequence_length', 60) # Default 60
        self.batch_size = strategy_config.get('batch_size', 32) # Default 32
        self.outlier_std_threshold = strategy_config.get('outlier_std_threshold', 3.0) # Default 3.0

        # Initialize scalers
        self.price_scaler = MinMaxScaler()
        self.feature_scaler = RobustScaler() # Using RobustScaler for features sensitive to outliers
        # Additional scalers for different feature types (initialized on demand)
        # self.volume_scaler and self.indicator_scaler will be created when needed

        # Track processing statistics
        self.stats = ProcessingStats(
            input_rows=0,
            output_rows=0,
            input_columns=0,
            output_columns=0,
            missing_value_count=0,
            outliers_removed=0,
            memory_usage_mb=0,
            processing_time_ms=0
        )

        # Define indicator groups (these could potentially also come from config)
        self.trend_indicators = ['SMA', 'EMA', 'TEMA', 'MACD', 'ADX']
        self.momentum_indicators = ['RSI', 'CCI', 'STOCH', 'MOM', 'ROC', 'WILLR']
        self.volatility_indicators = ['ATR', 'BBANDS', 'NATR']
        self.volume_indicators = ['OBV', 'AD']

        # Features to include based on config
        self.enabled_features = {
            'trend': strategy_config.get('use_trend_indicators', True),
            'momentum': strategy_config.get('use_momentum_indicators', True),
            'volatility': strategy_config.get('use_volatility_indicators', True),
            'volume': strategy_config.get('use_volume_indicators', True),
            'time': strategy_config.get('use_time_features', True)
        }

        # Memory management parameters (from top-level config)
        self.max_batch_size = self.config.get('preprocessor_max_batch_size', 10000) # Default 10000

        logger.info("DataPreprocessor initialized with provided config.")

    def _validate_price_data(self, df: pd.DataFrame) -> bool:
        """
        Validate price data integrity.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            bool: True if data is valid, False otherwise
        """
        try:
            # Check for invalid price relationships
            invalid_high_low = (df['high'] < df['low']).any()
            if invalid_high_low:
                logger.error("Found rows where high < low")
                return False

            invalid_close_high = (df['close'] > df['high']).any()
            if invalid_close_high:
                logger.error("Found rows where close > high")
                return False

            invalid_close_low = (df['close'] < df['low']).any()
            if invalid_close_low:
                logger.error("Found rows where close < low")
                return False

            invalid_open_high = (df['open'] > df['high']).any()
            if invalid_open_high:
                logger.error("Found rows where open > high")
                return False

            invalid_open_low = (df['open'] < df['low']).any()
            if invalid_open_low:
                logger.error("Found rows where open < low")
                return False

            # Check for negative prices
            negative_prices = (df[['open', 'high', 'low', 'close']] <= 0).any().any()
            if negative_prices:
                logger.error("Found negative or zero prices")
                return False

            # Check for extreme price movements (>50% in one candle)
            if len(df) > 1:
                price_changes = df['close'].pct_change().abs()
                extreme_moves = (price_changes > 0.5).any()
                if extreme_moves:
                    max_change = price_changes.max()
                    logger.warning(f"Found extreme price movement: {max_change:.2%}")
                    # Don't fail validation for this, just warn

            # Check for volume data if present
            if 'volume' in df.columns:
                negative_volume = (df['volume'] < 0).any()
                if negative_volume:
                    logger.error("Found negative volume")
                    return False

            logger.debug("Price data validation passed")
            return True

        except Exception as e:
            logger.error(f"Error validating price data: {str(e)}")
            return False

    def _calculate_sma(self, series: pd.Series, window: int) -> pd.Series:
        """Calculate Simple Moving Average"""
        return series.rolling(window=window).mean()

    def _calculate_ema(self, series: pd.Series, window: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return series.ewm(span=window, adjust=False).mean()

    def _calculate_rsi(self, series: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _calculate_macd(self, series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        exp1 = series.ewm(span=fast, adjust=False).mean()
        exp2 = series.ewm(span=slow, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal, adjust=False).mean()
        return pd.DataFrame({
            'MACD': macd,
            'Signal': signal_line,
            'Histogram': macd - signal_line
        })

    def _calculate_bollinger_bands(self, series: pd.Series, window: int = 20, num_std: float = 2.0) -> pd.DataFrame:
        """Calculate Bollinger Bands"""
        sma = self._calculate_sma(series, window)
        std = series.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return pd.DataFrame({
            'Upper': upper_band,
            'Middle': sma,
            'Lower': lower_band
        })

    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=window).mean()

    def _calculate_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                            k_window: int = 14, d_window: int = 3) -> pd.DataFrame:
        """Calculate Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        k = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d = k.rolling(window=d_window).mean()
        return pd.DataFrame({
            'K': k,
            'D': d
        })

    def _add_technical_indicators(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Add technical indicators to the dataframe

        Args:
            df: Input dataframe with OHLCV data

        Returns:
            DataFrame with added technical indicators or None if error
        """
        try:
            # Create a copy to avoid modifying the original
            result = df.copy()

            # Calculate indicators
            result['SMA_20'] = self._calculate_sma(result['close'], 20)
            result['SMA_50'] = self._calculate_sma(result['close'], 50)
            result['SMA_200'] = self._calculate_sma(result['close'], 200)

            result['EMA_12'] = self._calculate_ema(result['close'], 12)
            result['EMA_26'] = self._calculate_ema(result['close'], 26)

            result['RSI'] = self._calculate_rsi(result['close'])

            macd = self._calculate_macd(result['close'])
            result['MACD'] = macd['MACD']
            result['MACD_Signal'] = macd['Signal']
            result['MACD_Hist'] = macd['Histogram']

            bollinger = self._calculate_bollinger_bands(result['close'])
            result['BB_Upper'] = bollinger['Upper']
            result['BB_Middle'] = bollinger['Middle']
            result['BB_Lower'] = bollinger['Lower']

            result['ATR'] = self._calculate_atr(result['high'], result['low'], result['close'])

            stoch = self._calculate_stochastic(result['high'], result['low'], result['close'])
            result['Stoch_K'] = stoch['K']
            result['Stoch_D'] = stoch['D']

            # Add momentum indicators
            result['Momentum'] = result['close'].pct_change(periods=10)
            result['ROC'] = ((result['close'] - result['close'].shift(10)) / result['close'].shift(10)) * 100

            # Add volume indicators
            result['Volume_SMA'] = self._calculate_sma(result['volume'], 20)
            result['Volume_EMA'] = self._calculate_ema(result['volume'], 20)

            # Add volatility indicators
            result['Volatility'] = result['close'].pct_change().rolling(window=20).std() * np.sqrt(252)

            # Drop rows with NaN values (usually at the beginning due to indicator lookback)
            orig_len = len(result)
            result = result.dropna()
            dropped = orig_len - len(result)
            if dropped > 0:
                logger.info(f"Dropped {dropped} rows with NaN values after adding indicators")

            return result

        except Exception as e:
            logger.error(f"Error adding technical indicators: {str(e)}")
            return None

    def preprocess_data(self, df: pd.DataFrame, feature_cols: List[str]) -> Tuple[Optional[pd.DataFrame], Optional[np.ndarray]]:
        """Main method to preprocess raw data for prediction."""
        # CRITICAL FIX: Add comprehensive input validation
        if df is None:
            logger.error("Input DataFrame is None")
            return None, None

        if df.empty:
            logger.error("Input DataFrame is empty")
            return None, None

        if not isinstance(df, pd.DataFrame):
            logger.error(f"Input must be a pandas DataFrame, got {type(df)}")
            return None, None

        if not feature_cols or not isinstance(feature_cols, list):
            logger.error(f"feature_cols must be a non-empty list, got {type(feature_cols)}: {feature_cols}")
            return None, None

        if len(df) < self.sequence_length:
            logger.error(f"Input DataFrame has {len(df)} rows, but sequence_length is {self.sequence_length}. Need at least {self.sequence_length} rows.")
            return None, None

        # Check for required OHLCV columns
        required_ohlcv = ['open', 'high', 'low', 'close']
        missing_ohlcv = [col for col in required_ohlcv if col not in df.columns]
        if missing_ohlcv:
            logger.error(f"Missing required OHLCV columns: {missing_ohlcv}")
            return None, None

        # Validate price data integrity
        if not self._validate_price_data(df):
            logger.error("Price data validation failed")
            return None, None

        start_time = time.time()
        initial_memory = psutil.Process(os.getpid()).memory_info().rss
        self.stats.input_rows = len(df)
        self.stats.input_columns = len(df.columns)

        try:
            logger.debug(f"Starting data preprocessing with {len(df)} rows and {len(df.columns)} columns")
            logger.debug(f"Input DataFrame columns: {list(df.columns)}")
            logger.debug(f"Required feature columns: {feature_cols}")

            # 1. Handle Missing Values
            df, missing_count = self._handle_missing_values(df)
            self.stats.missing_value_count = missing_count
            if df is None or df.empty:
                logger.warning("Dataframe empty after handling missing values.")
                return None, None
            logger.debug(f"After handling missing values - columns: {list(df.columns)}")

            # 2. Add Technical Indicators (Optional, based on config)
            # This might depend on whether feature_cols includes them already
            # df = self._add_technical_indicators(df)
            # if df is None or df.empty: return None

            # 3. Add Derived Features (like time features, etc.)
            df = self._add_derived_features(df)
            if df is None or df.empty:
                logger.warning("Dataframe empty after adding derived features.")
                return None, None
            logger.debug(f"After adding derived features - columns: {list(df.columns)}")

            # 4. Handle Outliers
            df, outliers_removed = self._handle_outliers(df, feature_cols)
            self.stats.outliers_removed = outliers_removed
            if df is None or df.empty:
                logger.warning("Dataframe empty after handling outliers.")
                return None, None
            logger.debug(f"After handling outliers - columns: {list(df.columns)}")

            # 5. Select Final Features needed by the model
            if not all(col in df.columns for col in feature_cols):
                missing_req_cols = [col for col in feature_cols if col not in df.columns]
                logger.error(f"Required feature columns missing after processing: {missing_req_cols}")
                logger.error(f"Available columns: {list(df.columns)}")
                return None, None
            df_features = df[feature_cols]
            logger.debug(f"After selecting features - columns: {list(df_features.columns)}")

            # 6. Scale Features
            df_scaled = self._scale_features(df_features)
            if df_scaled is None or df_scaled.empty:
                logger.error("Feature scaling failed.")
                return None, None
            logger.debug(f"After scaling features - columns: {list(df_scaled.columns)}")

            # 7. Prepare Sequences
            sequences = self._prepare_sequences(df_scaled)
            if sequences is None:
                 logger.error("Sequence preparation failed.")
                 return None, None

            # Update stats
            final_memory = psutil.Process(os.getpid()).memory_info().rss
            self.stats.output_rows = len(sequences) if sequences is not None else 0
            self.stats.output_columns = sequences.shape[2] if sequences is not None and sequences.ndim == 3 else 0
            self.stats.memory_usage_mb = (final_memory - initial_memory) / (1024 * 1024)
            self.stats.processing_time_ms = (time.time() - start_time) * 1000
            logger.info(f"Preprocessing complete. Time: {self.stats.processing_time_ms:.2f}ms, Output Shape: {sequences.shape if sequences is not None else 'None'}")

            return df, sequences # Return both the processed DataFrame and the sequences (X)

        except Exception as e:
            logger.error(f"Error during data preprocessing: {e}", exc_info=True)
            # Handle error (e.g., using self.error_handler if passed in)
            return None, None

    def get_stats(self) -> ProcessingStats:
        """Return the latest processing statistics."""
        return self.stats

    def _handle_missing_values(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """
        Handle missing values in the data

        Args:
            df: DataFrame with possibly missing values

        Returns:
            Tuple of (cleaned DataFrame, count of missing values)
        """
        # Make a copy to avoid modifying the original
        df = df.copy()

        # Get missing value count before handling
        missing_count = df.isna().sum().sum()

        if missing_count > 0:
            logger.warning(f"Found {missing_count} missing values in data")

            # First, check for columns with excessive missing values (>50%)
            missing_pct = df.isna().mean() * 100
            excessive_missing = missing_pct[missing_pct > 50].index.tolist()

            if excessive_missing:
                logger.warning(f"Columns with >50% missing values: {excessive_missing}")
                # Drop columns with excessive missing values
                df = df.drop(columns=excessive_missing)
                logger.info(f"Dropped {len(excessive_missing)} columns with excessive missing values")

            # For price data, use forward fill then backward fill
            # This maintains the last known price until new data is available
            for col in ['open', 'high', 'low', 'close']:
                if col in df.columns:
                    df[col] = df[col].ffill()  # Forward fill
                    df[col] = df[col].bfill()  # Backward fill

            # For volume, replace with median or 0
            if 'volume' in df.columns:
                df['volume'] = df['volume'].fillna(df['volume'].median())
                df['volume'] = df['volume'].fillna(0)

            # For all other columns, use more aggressive filling
            for col in df.columns:
                if col not in ['open', 'high', 'low', 'close', 'volume'] and df[col].isna().any():
                    # For indicators, use forward fill, backward fill, then median
                    df[col] = df[col].ffill()  # Forward fill
                    df[col] = df[col].bfill()  # Backward fill
                    if df[col].isna().any():
                        df[col] = df[col].fillna(df[col].median())
                        # If still have NaN (e.g., all NaN column), fill with 0
                        if df[col].isna().any():
                            df[col] = df[col].fillna(0)

            # Check if we still have missing values
            remaining_missing = df.isna().sum().sum()
            if remaining_missing > 0:
                # Count rows before dropping
                rows_before = len(df)

                # If we still have missing values, drop those rows
                df = df.dropna()

                # Count rows after dropping
                rows_after = len(df)
                rows_dropped = rows_before - rows_after

                logger.warning(f"Dropped {rows_dropped} rows with missing values ({rows_dropped/rows_before:.2%} of data)")

        return df, missing_count

    def _add_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add derived features and time-based features

        Args:
            df: DataFrame with price and indicator data

        Returns:
            DataFrame with additional derived features
        """
        # Make a copy to avoid modifying the original
        result = df.copy()

        try:
            # Add price-based features
            result['returns'] = result['close'].pct_change()
            result['log_returns'] = np.log(result['close'] / result['close'].shift(1))
            result['range'] = result['high'] - result['low']
            result['range_pct'] = result['range'] / result['close']

            # High-Low ratio
            result['high_low_ratio'] = result['high'] / result['low']

            # Distance from price to moving averages
            if 'SMA_20' in result.columns:
                result['price_to_sma20'] = result['close'] / result['SMA_20'] - 1

            if 'EMA_20' in result.columns:
                result['price_to_ema20'] = result['close'] / result['EMA_20'] - 1

            # Add time-based features if enabled
            if self.enabled_features.get('time', False) and 'time' in result.columns: # Use .get() for safety
                # Convert time to datetime if it's not already
                if not pd.api.types.is_datetime64_any_dtype(result['time']):
                    try: # Add try-except for robust datetime conversion
                        result['time'] = pd.to_datetime(result['time'])
                    except Exception as dt_err:
                        logger.warning(f"Could not convert 'time' column to datetime: {dt_err}. Skipping time features.")
                        # Optionally return result here if time features are critical
                        # return result # Or handle differently
                # Check again if conversion was successful before proceeding
                if pd.api.types.is_datetime64_any_dtype(result['time']):
                    # Extract time components
                    result['hour'] = result['time'].dt.hour
                    result['day_of_week'] = result['time'].dt.dayofweek
                    result['day_of_month'] = result['time'].dt.day
                    result['month'] = result['time'].dt.month

                    # Create cyclical time features
                    result['hour_sin'] = np.sin(2 * np.pi * result['hour'] / 24)
                    result['hour_cos'] = np.cos(2 * np.pi * result['hour'] / 24)
                    result['day_of_week_sin'] = np.sin(2 * np.pi * result['day_of_week'] / 7)
                    result['day_of_week_cos'] = np.cos(2 * np.pi * result['day_of_week'] / 7)

                    # Drop the original time column as it's not numeric
                    result = result.drop('time', axis=1, errors='ignore') # Add errors='ignore'

            # Handle NaN values in derived features more carefully
            orig_len_derived = len(result)

            # Fill NaN values in derived features instead of dropping rows
            # This preserves all the original OHLCV data
            if 'returns' in result.columns:
                # Fill first NaN with 0 (no return for first period)
                result['returns'] = result['returns'].fillna(0)

            if 'log_returns' in result.columns:
                # Fill first NaN with 0 (no log return for first period)
                result['log_returns'] = result['log_returns'].fillna(0)

            # Fill any remaining NaN values in other derived features
            for col in result.columns:
                if col not in ['open', 'high', 'low', 'close', 'volume']:  # Don't modify OHLCV
                    if result[col].isna().any():
                        # Forward fill, then backward fill, then fill with 0
                        result[col] = result[col].ffill().bfill().fillna(0)

            # Check if we still have any NaN values
            remaining_nan = result.isna().sum().sum()
            if remaining_nan > 0:
                logger.warning(f"Still have {remaining_nan} NaN values after filling derived features")
                # Only drop rows if we absolutely have to
                result = result.dropna()
                dropped_derived = orig_len_derived - len(result)
                if dropped_derived > 0:
                    logger.info(f"Dropped {dropped_derived} rows with remaining NaN values after adding derived features.")
            else:
                logger.debug("Successfully filled all NaN values in derived features without dropping rows")

            return result

        except Exception as e:
            logger.warning(f"Error adding derived features: {str(e)}")
            # Return original DataFrame if we encounter an error
            return df

    def _handle_outliers(self, df: pd.DataFrame, feature_cols: List[str]) -> Tuple[pd.DataFrame, int]:
        """
        Detect and handle outliers in the data

        Args:
            df: DataFrame with features

        Returns:
            Tuple of (cleaned DataFrame, count of outliers removed)
        """
        # Make a copy to avoid modifying the original
        result = df.copy()
        outliers_removed = 0

        try:
            # Only detect outliers in returns and some indicators
            outlier_columns = ['returns', 'log_returns', 'range_pct']

            for indicator in ['RSI', 'CCI', 'MOM', 'ROC', 'ATR']:
                if indicator in result.columns:
                    outlier_columns.append(indicator)

            # For each column, calculate z-score and identify outliers
            for col in outlier_columns:
                if col in result.columns:
                    # Calculate z-score
                    mean = result[col].mean()
                    std = result[col].std()
                    if std == 0:  # Skip if standard deviation is 0 (constant values)
                        continue

                    z_scores = np.abs((result[col] - mean) / std)

                    # Identify outliers
                    outliers = z_scores > self.outlier_std_threshold
                    current_outliers = outliers.sum()

                    if current_outliers > 0:
                        # Handle outliers - replace with threshold values (capping/flooring)
                        # median = result[col].median() # Median replacement is another option

                        upper_threshold = mean + self.outlier_std_threshold * std
                        lower_threshold = mean - self.outlier_std_threshold * std

                        # Apply capping/flooring vectorized
                        result.loc[outliers & (result[col] > upper_threshold), col] = upper_threshold
                        result.loc[outliers & (result[col] < lower_threshold), col] = lower_threshold

                        # Count outliers
                        outliers_removed += current_outliers

                        logger.debug(f"Handled {current_outliers} outliers in {col} using std threshold {self.outlier_std_threshold}")

            return result, outliers_removed

        except Exception as e:
            logger.warning(f"Error handling outliers: {str(e)}")
            # Return original DataFrame if we encounter an error
            return df, 0

    def _scale_features(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Scale features to be in similar ranges

        Args:
            df: DataFrame with features

        Returns:
            DataFrame with scaled features
        """
        try:
            # Make a copy for scaling
            df_scaled = df.copy()

            # Scale price columns
            # Use default empty list if 'price_columns' not in config or None
            price_columns_config = self.config.get('scaling', {}).get('price_columns', ['open', 'high', 'low', 'close'])
            price_columns = [col for col in price_columns_config if col in df.columns]

            if price_columns:
                price_data = df_scaled[price_columns].values
                # Check if scaler has been fitted
                if hasattr(self.price_scaler, 'n_samples_seen_') and self.price_scaler.n_samples_seen_ > 0:
                    price_data_scaled = self.price_scaler.transform(price_data) # Use transform if already fitted
                else:
                    price_data_scaled = self.price_scaler.fit_transform(price_data) # Use fit_transform first time
                df_scaled[price_columns] = price_data_scaled
            else:
                 logger.warning("No price columns found or specified for scaling.")

            # Scale volume columns if not empty
            # Use default empty list if 'volume_columns' not in config or None
            volume_columns_config = self.config.get('scaling', {}).get('volume_columns', ['volume'])
            volume_columns = [col for col in volume_columns_config if col in df.columns]

            if volume_columns:
                volume_data = df_scaled[volume_columns].values.reshape(-1, len(volume_columns))

                # CRITICAL FIX: Use separate volume scaler to avoid losing fitted state
                if not hasattr(self, 'volume_scaler'):
                    self.volume_scaler = RobustScaler()

                # Check if volume scaler has been fitted
                if hasattr(self.volume_scaler, 'n_samples_seen_') and self.volume_scaler.n_samples_seen_ > 0:
                    volume_data_scaled = self.volume_scaler.transform(volume_data)
                else:
                    volume_data_scaled = self.volume_scaler.fit_transform(volume_data)
                df_scaled[volume_columns] = volume_data_scaled
            else:
                 logger.warning("No volume columns found or specified for scaling.")

            # Scale indicator columns
            # Infer indicator columns as those not in price or volume lists
            indicator_columns = [col for col in df.columns
                              if col not in price_columns and col not in volume_columns]

            if indicator_columns:
                indicator_data = df_scaled[indicator_columns].values

                # CRITICAL FIX: Use separate indicator scaler to avoid losing fitted state
                if not hasattr(self, 'indicator_scaler'):
                    self.indicator_scaler = RobustScaler()

                # Check if indicator scaler has been fitted
                if hasattr(self.indicator_scaler, 'n_samples_seen_') and self.indicator_scaler.n_samples_seen_ > 0:
                    indicator_data_scaled = self.indicator_scaler.transform(indicator_data)
                else:
                    indicator_data_scaled = self.indicator_scaler.fit_transform(indicator_data)
                df_scaled[indicator_columns] = indicator_data_scaled
            else:
                logger.info("No remaining indicator columns to scale.")

            return df_scaled

        except Exception as e:
            logger.warning(f"Error scaling features: {str(e)}")
            # Return original DataFrame if we encounter an error
            return None

    def _prepare_sequences(self, df_scaled: pd.DataFrame) -> Optional[np.ndarray]:
        """Prepare sequences from the scaled feature dataframe."""
        try:
            data = df_scaled.values
            if len(data) < self.sequence_length:
                logger.warning(f"Insufficient data length ({len(data)}) to create sequence of length {self.sequence_length}.")
                return None

            sequences = []
            # Prepare sequences ending at each step for prediction
            for i in range(len(data) - self.sequence_length + 1):
                sequence = data[i : i + self.sequence_length]
                sequences.append(sequence)

            if not sequences:
                return None

            return np.array(sequences)

        except Exception as e:
            logger.error(f"Error preparing sequences: {e}", exc_info=True)
            return None

    def save_scalers(self, path: str) -> bool:
        """
        Save all fitted scalers to disk for later use.

        Args:
            path: Directory path to save scalers

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import pickle
            from pathlib import Path

            scaler_dir = Path(path)
            scaler_dir.mkdir(parents=True, exist_ok=True)

            scalers = {
                'price_scaler': self.price_scaler,
                'feature_scaler': self.feature_scaler
            }

            # Add optional scalers if they exist
            if hasattr(self, 'volume_scaler'):
                scalers['volume_scaler'] = self.volume_scaler
            if hasattr(self, 'indicator_scaler'):
                scalers['indicator_scaler'] = self.indicator_scaler

            # Save scalers
            scaler_file = scaler_dir / "scalers.pkl"
            with open(scaler_file, 'wb') as f:
                pickle.dump(scalers, f)

            logger.info(f"Saved {len(scalers)} scalers to {scaler_file}")
            return True

        except Exception as e:
            logger.error(f"Error saving scalers: {str(e)}")
            return False

    def load_scalers(self, path: str) -> bool:
        """
        Load previously fitted scalers from disk.

        Args:
            path: Directory path containing saved scalers

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import pickle
            from pathlib import Path

            scaler_file = Path(path) / "scalers.pkl"
            if not scaler_file.exists():
                logger.warning(f"Scaler file not found: {scaler_file}")
                return False

            # Load scalers
            with open(scaler_file, 'rb') as f:
                scalers = pickle.load(f)

            # Restore scalers
            self.price_scaler = scalers.get('price_scaler', MinMaxScaler())
            self.feature_scaler = scalers.get('feature_scaler', RobustScaler())

            if 'volume_scaler' in scalers:
                self.volume_scaler = scalers['volume_scaler']
            if 'indicator_scaler' in scalers:
                self.indicator_scaler = scalers['indicator_scaler']

            logger.info(f"Loaded {len(scalers)} scalers from {scaler_file}")
            return True

        except Exception as e:
            logger.error(f"Error loading scalers: {str(e)}")
            return False

    def get_scaler_info(self) -> Dict[str, Any]:
        """
        Get information about the current state of all scalers.

        Returns:
            Dict containing scaler information
        """
        info = {}

        # Check price scaler
        if hasattr(self.price_scaler, 'n_samples_seen_'):
            info['price_scaler'] = {
                'fitted': self.price_scaler.n_samples_seen_ > 0,
                'samples_seen': self.price_scaler.n_samples_seen_,
                'type': type(self.price_scaler).__name__
            }
        else:
            info['price_scaler'] = {'fitted': False, 'type': type(self.price_scaler).__name__}

        # Check feature scaler
        if hasattr(self.feature_scaler, 'n_samples_seen_'):
            info['feature_scaler'] = {
                'fitted': self.feature_scaler.n_samples_seen_ > 0,
                'samples_seen': self.feature_scaler.n_samples_seen_,
                'type': type(self.feature_scaler).__name__
            }
        else:
            info['feature_scaler'] = {'fitted': False, 'type': type(self.feature_scaler).__name__}

        # Check optional scalers
        if hasattr(self, 'volume_scaler'):
            if hasattr(self.volume_scaler, 'n_samples_seen_'):
                info['volume_scaler'] = {
                    'fitted': self.volume_scaler.n_samples_seen_ > 0,
                    'samples_seen': self.volume_scaler.n_samples_seen_,
                    'type': type(self.volume_scaler).__name__
                }
            else:
                info['volume_scaler'] = {'fitted': False, 'type': type(self.volume_scaler).__name__}

        if hasattr(self, 'indicator_scaler'):
            if hasattr(self.indicator_scaler, 'n_samples_seen_'):
                info['indicator_scaler'] = {
                    'fitted': self.indicator_scaler.n_samples_seen_ > 0,
                    'samples_seen': self.indicator_scaler.n_samples_seen_,
                    'type': type(self.indicator_scaler).__name__
                }
            else:
                info['indicator_scaler'] = {'fitted': False, 'type': type(self.indicator_scaler).__name__}

        return info