#!/bin/bash
# Train all TFT models on all timeframes
# This script provides a unified interface for training TFT models

echo "====================================================="
echo "Training All TFT Models on All Timeframes"
echo "====================================================="

# Set error handling
set -e

# Define timeframes
TIMEFRAMES=("M5" "M15" "M30" "H1" "H4")
SYMBOL="BTCUSD.a"

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "ERROR: Python is not installed or not in PATH"
    exit 1
fi

# Check if required training script exists
if [ ! -f "train_tft_pytorch.py" ]; then
    echo "ERROR: train_tft_pytorch.py not found"
    echo "Please ensure the TFT training script exists"
    exit 1
fi

echo "Starting TFT model training..."
echo "Symbol: $SYMBOL"
echo "Timeframes: ${TIMEFRAMES[*]}"
echo ""

# Train TFT models for each timeframe
for timeframe in "${TIMEFRAMES[@]}"; do
    echo "Training TFT model for $SYMBOL on $timeframe..."
    
    # Run the TFT training script
    if python train_tft_pytorch.py --timeframe "$timeframe" --symbol "$SYMBOL"; then
        echo "✓ Successfully trained TFT model for $timeframe"
    else
        echo "✗ Failed to train TFT model for $timeframe"
        echo "Check logs for details"
        # Continue with other timeframes instead of exiting
    fi
    
    echo ""
done

echo "====================================================="
echo "TFT Model Training Complete"
echo "====================================================="

# Check if any models were created
MODEL_DIR="models"
if [ -d "$MODEL_DIR" ]; then
    echo "Checking for created TFT models..."
    find "$MODEL_DIR" -name "*tft*" -type f | head -10
else
    echo "Warning: Models directory not found"
fi

echo ""
echo "Training session completed."
echo "Check the logs directory for detailed training logs."
