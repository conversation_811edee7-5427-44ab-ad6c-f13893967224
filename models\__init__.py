"""
Models package for the trading bot.

This package contains all machine learning models used for time series forecasting:
- LSTM models (PyTorch implementation)
- TFT models (TensorFlow implementation) 
- ARIMA models (statsmodels implementation)
- Ensemble models for combining predictions
"""

from .pytorch_lstm_model import LSTMModel, LSTMModelWrapper
from .tft_model import TFTModel
from .ensemble_arima_model import EnsembleARIMAModel

# Import ARIMA model from utils for backward compatibility
try:
    from utils.arima_trainer import ARIMAModel
except ImportError:
    ARIMAModel = None

__all__ = [
    'LSTMModel',
    'LSTMModelWrapper',
    'TFTModel',
    'EnsembleARIMAModel',
    'ARIMAModel'
]
