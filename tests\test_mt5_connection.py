"""
Test script to validate MT5 connection handling and data collection.
"""
import os
import sys
import time
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import pandas as pd
import logging
# Try to import from credentials first, fall back to test config if not available
try:
    from config.credentials import MT5_TERMINALS as mt5_configs
except ImportError:
    from tests.test_config import mt5_configs

# Use comprehensive logging system
try:
    from utils.comprehensive_logging import get_main_logger
    logger = get_main_logger()
except ImportError:
    # Fallback to basic logging if comprehensive logging not available
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

def test_connection_initialization(terminal_id: int) -> bool:
    """Test MT5 connection initialization."""
    try:
        logger.info(f"\nTesting connection initialization for terminal {terminal_id}...")
        config = mt5_configs[terminal_id]

        # Initialize data collector
        from data.data_collector import MT5DataCollector
        collector = MT5DataCollector(config)

        # Verify connection
        if not collector.connected:
            logger.error("Failed to establish initial connection")
            return False

        # Verify account info
        account_info = mt5.account_info()
        if account_info is None:
            logger.error("Failed to get account info")
            return False

        logger.info(f"Successfully connected to account {account_info.login}")
        return True

    except Exception as e:
        logger.error(f"Error in connection initialization test: {str(e)}")
        return False

def test_connection_recovery(terminal_id: int) -> bool:
    """Test connection recovery after disconnection."""
    try:
        logger.info(f"\nTesting connection recovery for terminal {terminal_id}...")
        config = mt5_configs[terminal_id]

        # Initialize data collector
        from data.data_collector import MT5DataCollector
        collector = MT5DataCollector(config)

        # Simulate disconnection without calling mt5.shutdown()
        # DO NOT call mt5.shutdown() as it disables Algo Trading
        logger.info("Simulating disconnection without calling mt5.shutdown()")
        collector.connected = False

        # Wait a moment
        time.sleep(2)

        # Attempt recovery
        if not collector._initialize_mt5():
            logger.error("Failed to recover connection")
            return False

        # Verify reconnection
        if not collector.connected:
            logger.error("Connection not restored")
            return False

        logger.info("Successfully recovered connection")
        return True

    except Exception as e:
        logger.error(f"Error in connection recovery test: {str(e)}")
        return False

def test_data_collection(terminal_id: int) -> bool:
    """Test data collection functionality."""
    try:
        logger.info(f"\nTesting data collection for terminal {terminal_id}...")
        config = mt5_configs[terminal_id]

        # Initialize data collector
        from data.data_collector import MT5DataCollector
        collector = MT5DataCollector(config)

        # Test historical data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        data = collector.get_historical_data(
            symbol="BTCUSD.a",
            timeframe="M5",
            start_date=start_date,
            end_date=end_date
        )

        if data is None:
            logger.error("Failed to fetch historical data")
            return False

        logger.info(f"Successfully fetched {len(data)} historical records")

        # Test real-time data
        realtime_data = collector.get_realtime_data(
            symbol="BTCUSD.a",
            timeframe="M5",
            num_bars=10
        )

        if realtime_data is None:
            logger.error("Failed to fetch real-time data")
            return False

        logger.info(f"Successfully fetched {len(realtime_data)} real-time records")
        return True

    except Exception as e:
        logger.error(f"Error in data collection test: {str(e)}")
        return False

def test_health_check(terminal_id: int) -> bool:
    """Test health check functionality."""
    try:
        logger.info(f"\nTesting health check for terminal {terminal_id}...")
        config = mt5_configs[terminal_id]

        # Initialize data collector
        from data.data_collector import MT5DataCollector
        collector = MT5DataCollector(config)

        # Perform health check
        if not collector.perform_health_check():
            logger.error("Initial health check failed")
            return False

        # Simulate connection issue without calling mt5.shutdown()
        # DO NOT call mt5.shutdown() as it disables Algo Trading
        logger.info("Simulating connection issue without calling mt5.shutdown()")
        collector.connected = False

        # Wait for next health check
        time.sleep(65)  # Wait slightly longer than health check interval

        # Perform health check again
        if not collector.perform_health_check():
            logger.error("Recovery health check failed")
            return False

        logger.info("Health check system working correctly")
        return True

    except Exception as e:
        logger.error(f"Error in health check test: {str(e)}")
        return False

def main():
    """Run all tests for each terminal."""
    logger.info("Starting MT5 connection tests...")

    success_count = 0
    total_tests = 0

    for terminal_id in mt5_configs:
        logger.info(f"\nTesting terminal {terminal_id}...")

        # Run all tests
        tests = [
            test_connection_initialization,
            test_connection_recovery,
            test_data_collection,
            test_health_check
        ]

        terminal_success = 0
        for test in tests:
            total_tests += 1
            if test(terminal_id):
                terminal_success += 1
                success_count += 1

        logger.info(f"Terminal {terminal_id}: {terminal_success}/{len(tests)} tests passed")

    logger.info(f"\nTest Results: {success_count}/{total_tests} tests passed")

    if success_count != total_tests:
        logger.error("Some tests failed. Please check the logs above.")
        sys.exit(1)
    else:
        logger.info("All tests passed successfully!")
        sys.exit(0)

if __name__ == "__main__":
    main()