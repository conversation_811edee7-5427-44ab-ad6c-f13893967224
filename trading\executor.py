"""
Trade execution module for executing trades through MT5.
"""
import logging
import math
from typing import Dict, List, Optional, Any
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import time
import threading
import json
import os

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
from utils.error_handler import <PERSON>rror<PERSON>and<PERSON>
from config.unified_config import TradingConfig

logger = logging.getLogger(__name__)

class TradeExecutor:
    """
    Trade executor class for executing trades through MT5.

    Handles order placement, order modification, and order closure.
    """

    def __init__(self,
                 mt5_manager: MT5ConnectionManager,
                 terminal_id: int,
                 config: TradingConfig,
                 error_handler: Optional[ErrorHandler] = None):
        """
        Initialize the trade executor.

        Args:
            mt5_manager: MT5 connection manager
            terminal_id: MT5 terminal ID
            config: Trading configuration
            error_handler: Error handler
        """
        self.mt5_manager = mt5_manager
        self.terminal_id = terminal_id
        self.config = config
        self.error_handler = error_handler
        self._lock = threading.RLock()
        self.active_orders = {}
        self.profit_loss = 0.0
        self.trade_count = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.trade_history = []  # Add missing trade_history attribute
        # Check if config has a strategy attribute or is itself a strategy config
        if hasattr(config, 'strategy'):
            self.symbol = config.strategy.symbol
            self.max_positions = config.strategy.max_positions
        else:
            # Assume config is the strategy config itself
            self.symbol = config.symbol if hasattr(config, 'symbol') else "BTCUSD.a"
            self.max_positions = config.max_positions if hasattr(config, 'max_positions') else 3

        # BTCUSD.a specific parameters
        self.point_value = 0.01  # BTCUSD.a point value (more accurate)
        self.min_lot_size = 0.01  # Minimum lot size for BTCUSD.a
        self.max_lot_size = 0.5  # Maximum lot size for BTCUSD.a
        self.leverage = 1  # BTCUSD.a leverage
        self.max_slippage_pips = 50  # Reasonable slippage for BTCUSD.a
        self.execution_timeout = 30  # Increased timeout for crypto execution
        self.max_spread_pips = 5000  # Adjusted for BTCUSD.a crypto volatility (was 100)
        self.stop_loss_pips = 5000  # Increased for BTCUSD.a crypto volatility (was 200)
        self.take_profit_pips = 10000  # Increased for BTCUSD.a crypto volatility (was 400)

        # Initialize circuit breaker (simple implementation)
        self.order_circuit = self._create_simple_circuit_breaker()

        # Setup trading history persistence
        self._setup_trading_history_persistence()

        logger.info(f"TradeExecutor initialized for terminal {terminal_id} trading BTCUSD.a")

    def _setup_trading_history_persistence(self):
        """Setup trading history persistence directories and files."""
        try:
            # Create trading history directories
            self.trading_history_dir = f"logs/trading_history/terminal_{self.terminal_id}"

            # Create directories if they don't exist
            os.makedirs(f"{self.trading_history_dir}/trades", exist_ok=True)
            os.makedirs(f"{self.trading_history_dir}/performance", exist_ok=True)
            os.makedirs(f"{self.trading_history_dir}/mt5_history", exist_ok=True)

            # Setup file paths
            today = datetime.now().strftime('%Y%m%d')
            self.trades_log_file = f"{self.trading_history_dir}/trades/executor_trades_{today}.json"
            self.performance_log_file = f"{self.trading_history_dir}/performance/executor_performance_{today}.json"
            self.mt5_history_log_file = f"{self.trading_history_dir}/mt5_history/executor_mt5_history_{today}.json"

            logger.info(f"Trading history persistence setup for TradeExecutor Terminal {self.terminal_id}")

        except Exception as e:
            logger.error(f"Failed to setup trading history persistence for TradeExecutor Terminal {self.terminal_id}: {e}")

    def _save_trade_to_history(self, trade_info, action_type="executed"):
        """Save trade information to persistent history file."""
        try:
            trade_data = {
                "timestamp": datetime.now().isoformat(),
                "action_type": action_type,  # executed, closed, updated
                "terminal_id": self.terminal_id,
                "executor_source": "TradeExecutor",
                **trade_info  # Include all trade info
            }

            # Read existing trades
            trades_history = []
            if os.path.exists(self.trades_log_file):
                try:
                    with open(self.trades_log_file, 'r') as f:
                        trades_history = json.load(f)
                except:
                    trades_history = []

            # Add new trade
            trades_history.append(trade_data)

            # Write back to file
            with open(self.trades_log_file, 'w') as f:
                json.dump(trades_history, f, indent=2, default=str)

            logger.debug(f"Saved trade {action_type} to history: Ticket {trade_data.get('ticket')}")

        except Exception as e:
            logger.error(f"Failed to save trade to history: {e}")

    def _save_performance_to_history(self):
        """Save performance metrics to persistent history file."""
        try:
            performance_data = {
                "timestamp": datetime.now().isoformat(),
                "terminal_id": self.terminal_id,
                "executor_source": "TradeExecutor",
                "symbol": self.symbol,
                "profit_loss": self.profit_loss,
                "trade_count": self.trade_count,
                "winning_trades": self.winning_trades,
                "losing_trades": self.losing_trades,
                "active_orders_count": len(self.active_orders),
                "trade_history_count": len(self.trade_history),
                "win_rate": (self.winning_trades / max(1, self.trade_count)) * 100,
                "avg_profit_per_trade": self.profit_loss / max(1, self.trade_count)
            }

            # Read existing performance data
            performance_history = []
            if os.path.exists(self.performance_log_file):
                try:
                    with open(self.performance_log_file, 'r') as f:
                        performance_history = json.load(f)
                except:
                    performance_history = []

            # Add new performance data
            performance_history.append(performance_data)

            # Keep only last 1000 entries to prevent file from growing too large
            if len(performance_history) > 1000:
                performance_history = performance_history[-1000:]

            # Write back to file
            with open(self.performance_log_file, 'w') as f:
                json.dump(performance_history, f, indent=2, default=str)

            logger.debug(f"Saved performance metrics to history")

        except Exception as e:
            logger.error(f"Failed to save performance to history: {e}")

    def _save_mt5_history_to_file(self):
        """Save MT5 native trading history to persistent file."""
        try:
            # Get MT5 trading history
            history_data = self.get_trade_history(days=1)

            if history_data:
                mt5_history_data = {
                    "timestamp": datetime.now().isoformat(),
                    "terminal_id": self.terminal_id,
                    "executor_source": "TradeExecutor",
                    "symbol": self.symbol,
                    "history_orders": history_data.get("orders", []),
                    "history_deals": history_data.get("deals", []),
                    "orders_count": len(history_data.get("orders", [])),
                    "deals_count": len(history_data.get("deals", []))
                }

                # Write to file (overwrite daily)
                with open(self.mt5_history_log_file, 'w') as f:
                    json.dump(mt5_history_data, f, indent=2, default=str)

                logger.debug(f"Saved MT5 history to file: {mt5_history_data['orders_count']} orders, {mt5_history_data['deals_count']} deals")

        except Exception as e:
            logger.error(f"Failed to save MT5 history to file: {e}")

    def execute_trade(self, signal) -> Dict[str, Any]:
        """
        Execute a trade based on the provided signal.
        Adjusted for BTCUSD.a execution characteristics.

        Args:
            signal: Trading signal (TradingSignal object or dict) with action, confidence, etc.

        Returns:
            Dict[str, Any]: Trade execution result
        """
        try:
            # Extract signal information - handle both dict and TradingSignal object
            if hasattr(signal, 'action'):
                # TradingSignal object
                action = getattr(signal, 'action', 'HOLD')
                confidence = getattr(signal, 'confidence', 0.0)
                volume = getattr(signal, 'volume', None)
            else:
                # Dictionary
                action = signal.get('action', 'HOLD')
                confidence = signal.get('confidence', 0.0)
                volume = signal.get('volume', None)

            # Skip if action is HOLD
            if action == 'HOLD':
                logger.info("Signal action is HOLD, no trade to execute")
                return {
                    'success': True,
                    'message': 'No trade executed (HOLD signal)',
                    'ticket': None,
                    'profit_loss': 0.0
                }

            # Get account info
            account_info = self.get_account_info()

            # Check if we have enough free margin
            if hasattr(account_info, 'margin_free') and account_info.margin_free <= 0:
                logger.warning("Not enough free margin to execute trade")
                return {
                    'success': False,
                    'message': 'Not enough free margin',
                    'ticket': None,
                    'profit_loss': 0.0
                }

            # Get current price
            price_info = self.get_current_price(self.symbol)

            # Check spread (adjusted for BTCUSD.a)
            current_spread = price_info.get('spread', 0.0)
            if current_spread > self.max_spread_pips:
                logger.warning(f"Spread too high: {current_spread} pips > {self.max_spread_pips} pips")
                return {
                    'success': False,
                    'message': f'Spread too high: {current_spread} pips',
                    'ticket': None,
                    'profit_loss': 0.0
                }

            # Calculate position size (adjusted for BTCUSD.a)
            if volume is None:
                volume = self.calculate_position_size(account_info)

            # Ensure volume is within BTCUSD.a limits
            volume = max(self.min_lot_size, min(volume, self.max_lot_size))

            # Set up trade parameters
            trade_params = {
                'action': mt5.TRADE_ACTION_DEAL,
                'symbol': self.symbol,
                'volume': volume,
                'type': mt5.ORDER_TYPE_BUY if action == 'BUY' else mt5.ORDER_TYPE_SELL,
                'price': price_info.get('ask' if action == 'BUY' else 'bid', 0.0),
                'deviation': self.max_slippage_pips,
                'magic': 123456,  # Magic number for identifying bot trades
                'comment': f"Bot trade - {confidence:.2f} confidence",
                'type_time': mt5.ORDER_TIME_GTC,
                'type_filling': mt5.ORDER_FILLING_IOC,
            }

            # Add SL/TP if configured
            point_value = self.point_value

            # Get symbol info to check minimum stop level
            symbol_info = mt5.symbol_info(self.symbol)
            min_stop_level = 0
            if symbol_info:
                min_stop_level = getattr(symbol_info, 'trade_stops_level', 0)
                actual_point = symbol_info.point
            else:
                actual_point = point_value

            if self.stop_loss_pips > 0:
                # For BTCUSD.a, use proper point calculation
                if symbol_info:
                    # Use symbol's actual point value (0.01 for BTCUSD.a)
                    sl_distance_points = max(self.stop_loss_pips, min_stop_level)
                    sl_distance = sl_distance_points * actual_point
                else:
                    # Fallback for BTCUSD.a - direct pip calculation
                    sl_distance = self.stop_loss_pips * 0.01  # 0.01 is the point value for BTCUSD.a

                if action == 'BUY':
                    trade_params['sl'] = round(trade_params['price'] - sl_distance, 2)
                else:
                    trade_params['sl'] = round(trade_params['price'] + sl_distance, 2)

                # Validate stop loss is reasonable
                if trade_params['sl'] <= 0:
                    logger.warning(f"Invalid stop loss calculated: {trade_params['sl']}, removing SL")
                    trade_params.pop('sl', None)

            if self.take_profit_pips > 0:
                # For BTCUSD.a, use proper point calculation
                if symbol_info:
                    # Use symbol's actual point value (0.01 for BTCUSD.a)
                    tp_distance_points = max(self.take_profit_pips, min_stop_level)
                    tp_distance = tp_distance_points * actual_point
                else:
                    # Fallback for BTCUSD.a - direct pip calculation
                    tp_distance = self.take_profit_pips * 0.01  # 0.01 is the point value for BTCUSD.a

                if action == 'BUY':
                    trade_params['tp'] = round(trade_params['price'] + tp_distance, 2)
                else:
                    trade_params['tp'] = round(trade_params['price'] - tp_distance, 2)

                # Validate take profit is reasonable
                if trade_params['tp'] <= 0:
                    logger.warning(f"Invalid take profit calculated: {trade_params['tp']}, removing TP")
                    trade_params.pop('tp', None)

            # Log trade parameters for debugging
            logger.info(f"Trade parameters: {trade_params}")

            # Execute the trade with circuit breaker
            def _execute_order():
                # Ensure connection is established
                connection = self.mt5_manager.get_connection(self.terminal_id)
                if not connection or not connection.is_connected:
                    raise RuntimeError(f"MT5 connection not available for terminal {self.terminal_id}")

                # Check algorithmic trading status before executing trade
                terminal_info = mt5.terminal_info()
                if not terminal_info or not terminal_info.trade_allowed:
                    logger.error(f"Algorithmic trading is DISABLED for terminal {self.terminal_id}")
                    logger.error(f"Please enable algorithmic trading in MT5 terminal {self.terminal_id}")
                    logger.error(f"1. Open the terminal")
                    logger.error(f"2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                    logger.error(f"3. Verify that 'Algo Trading enabled' appears in the status bar")
                    raise RuntimeError(f"Algorithmic trading is disabled for terminal {self.terminal_id}")

                # Send trade request
                result = mt5.order_send(trade_params)

                if result is None:
                    error_code = mt5.last_error()
                    logger.error(f"Failed to send order: MT5 error code {error_code}")
                    raise RuntimeError(f"Failed to send order: MT5 error code {error_code}")

                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    # Log detailed error information
                    logger.error(f"Order failed with retcode {result.retcode}: {result.comment}")
                    if result.retcode == 10016:  # TRADE_RETCODE_INVALID_STOPS
                        logger.error(f"Invalid stops error. SL: {trade_params.get('sl')}, TP: {trade_params.get('tp')}")
                        logger.error(f"Price: {trade_params.get('price')}, Symbol info: {symbol_info}")
                    elif result.retcode == 10027:  # AutoTrading disabled by client
                        logger.error(f"CRITICAL: AutoTrading disabled by client for terminal {self.terminal_id}")
                        logger.error(f"To fix this issue:")
                        logger.error(f"1. Open MT5 terminal {self.terminal_id}")
                        logger.error(f"2. Click the 'Algo Trading' button in the toolbar until it turns GREEN")
                        logger.error(f"3. Verify 'Algo Trading enabled' appears in the status bar")
                        logger.error(f"4. Restart the trading bot")
                    raise RuntimeError(f"Order failed: {result.retcode} - {result.comment}")

                logger.info(f"Trade executed: {action} {volume} lots of {self.symbol} at {trade_params['price']}")

                # Store trade information for tracking
                trade_info = {
                    'ticket': result.order,
                    'action': action,
                    'symbol': self.symbol,
                    'volume': volume,
                    'price': trade_params['price'],
                    'timestamp': datetime.now(),
                    'sl': trade_params.get('sl'),
                    'tp': trade_params.get('tp'),
                    'profit_loss': 0.0,
                    'status': 'open'
                }

                self.active_orders[result.order] = trade_info
                self.trade_history.append(trade_info)

                # Save trade to persistent history
                self._save_trade_to_history(trade_info, "executed")

                return {
                    'success': True,
                    'message': f"{action} order executed",
                    'ticket': result.order,
                    'volume': volume,
                    'price': trade_params['price'],
                    'profit_loss': 0.0  # Profit/loss will be updated when position is closed
                }

            # Execute with circuit breaker
            return self.order_circuit.execute(
                _execute_order,
                circuit_name=f"order_execution_{self.terminal_id}"
            )

        except Exception as e:
            error_info = self.error_handler.handle_error(e, context={
                'component': 'trade_execution',
                'method': 'execute_trade',
                'signal': signal
            })

            logger.error(f"Trade execution failed: {error_info.error_message}")

            return {
                'success': False,
                'message': f"Trade failed: {str(e)}",
                'ticket': None,
                'profit_loss': 0.0
            }

    def close_trade(self, ticket: int) -> bool:
        """
        Close a specific trade.

        Args:
            ticket: Order ticket number

        Returns:
            bool: True if closed successfully, False otherwise
        """
        with self._lock:
            try:
                # Ensure connection is active
                connection = self.mt5_manager.get_connection(self.terminal_id)
                if not connection or not connection.is_connected:
                    logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                    return False

                # Get position info
                position = mt5.positions_get(ticket=ticket)
                if position is None or len(position) == 0:
                    logger.error(f"Position with ticket {ticket} not found")
                    return False

                position = position[0]

                # Determine position direction
                order_type = mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY

                # Create close request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": position.symbol,
                    "volume": position.volume,
                    "type": order_type,
                    "position": ticket,
                    "price": 0.0,  # Market price
                    "deviation": 10,
                    "magic": 123456,
                    "comment": "Close position",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK
                }

                # Send order
                result = mt5.order_send(request)

                if result is None:
                    error = mt5.last_error()
                    logger.error(f"Close order failed: {error}")
                    return False

                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    logger.error(f"Close order failed with code {result.retcode}: {result.comment}")
                    return False

                # Calculate profit/loss
                if ticket in self.active_orders:
                    entry_price = self.active_orders[ticket]["price"]
                    exit_price = result.price
                    volume = self.active_orders[ticket]["volume"]
                    action = self.active_orders[ticket]["action"]

                    if action == "buy":
                        profit = (exit_price - entry_price) * volume
                    else:
                        profit = (entry_price - exit_price) * volume

                    # Update profit/loss
                    self.profit_loss += profit

                    # Update win/loss count
                    if profit > 0:
                        self.winning_trades += 1
                    else:
                        self.losing_trades += 1

                    # Update trade history with closure info
                    for trade in self.trade_history:
                        if isinstance(trade, dict) and trade.get('ticket') == ticket:
                            trade['profit_loss'] = profit
                            trade['status'] = 'closed'
                            trade['close_time'] = datetime.now()
                            trade['close_price'] = exit_price
                            # Save updated trade to persistent history
                            self._save_trade_to_history(trade, "closed")
                            break

                    # Remove from active orders
                    del self.active_orders[ticket]

                logger.info(f"Position {ticket} closed successfully")
                return True

            except Exception as e:
                if self.error_handler:
                    self.error_handler.handle_error(
                        e,
                        context={
                            "method": "close_trade",
                            "ticket": ticket
                        }
                    )
                logger.error(f"Error closing trade: {str(e)}")
                return False

    def close_all_trades(self) -> bool:
        """
        Close all open positions.

        Returns:
            bool: True if all positions closed successfully, False otherwise
        """
        with self._lock:
            try:
                # Get all open positions
                positions = self.get_open_positions()
                if positions is None:
                    logger.error("Failed to get open positions")
                    return False

                # Close each position
                success = True
                for position in positions:
                    ticket = position.ticket
                    if not self.close_trade(ticket):
                        logger.error(f"Failed to close position {ticket}")
                        success = False

                return success

            except Exception as e:
                if self.error_handler:
                    self.error_handler.handle_error(
                        e,
                        context={"method": "close_all_trades"}
                    )
                logger.error(f"Error closing all trades: {str(e)}")
                return False

    def get_open_positions(self) -> Optional[List]:
        """
        Get all open positions.

        Returns:
            Optional[List]: List of open positions or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Get positions for our symbol
            positions = mt5.positions_get(symbol=self.symbol)
            if positions is None:
                error = mt5.last_error()
                logger.error(f"Failed to get positions: {error}")
                return None

            return positions

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "get_open_positions"}
                )
            logger.error(f"Error getting open positions: {str(e)}")
            return None

    def get_current_price(self, symbol: str) -> Optional[Dict[str, float]]:
        """
        Get current price information for a symbol.

        Args:
            symbol: Symbol to get price for

        Returns:
            Optional[Dict[str, float]]: Price info dict with 'bid', 'ask', 'spread' or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Get symbol information
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                error = mt5.last_error()
                logger.error(f"Failed to get symbol info: {error}")
                return None

            # Calculate spread in points
            spread_points = (symbol_info.ask - symbol_info.bid) / symbol_info.point

            # Return price information as dictionary
            return {
                'bid': symbol_info.bid,
                'ask': symbol_info.ask,
                'spread': spread_points,
                'point': symbol_info.point
            }

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={
                        "method": "get_current_price",
                        "symbol": symbol
                    }
                )
            logger.error(f"Error getting current price: {str(e)}")
            return None

    def _calculate_lot_size(self, confidence: float) -> float:
        """
        Calculate lot size based on confidence and risk management settings.

        Args:
            confidence: Signal confidence (0.0-1.0)

        Returns:
            float: Lot size to trade
        """
        try:
            # Get account info
            account_info = self.get_account_info()
            if account_info is None:
                logger.error("Failed to get account info for lot calculation")
                # Check if config has a strategy attribute or is itself a strategy config
                if hasattr(self.config, 'strategy'):
                    return self.config.strategy.lot_size
                else:
                    return getattr(self.config, 'lot_size', 0.01)

            balance = account_info.balance

            # Calculate risk-adjusted lot size
            if hasattr(self.config, 'strategy'):
                risk = self.config.strategy.risk_per_trade * confidence
            else:
                risk = getattr(self.config, 'risk_per_trade', 0.02) * confidence
            risk_amount = balance * risk

            # Get symbol information for margin calculation
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.warning("Failed to get symbol info for lot calculation")
                return self.config.strategy.lot_size

            contract_size = symbol_info.trade_contract_size

            # Simple lot calculation
            # More sophisticated calculation would consider leverage, margin, etc.
            lot_size = risk_amount / contract_size / 100

            # Ensure minimum and maximum lot size constraints
            min_lot = symbol_info.volume_min
            if hasattr(self.config, 'strategy'):
                max_lot = min(symbol_info.volume_max, self.config.strategy.lot_size * 2)
            else:
                max_lot = min(symbol_info.volume_max, getattr(self.config, 'lot_size', 0.01) * 2)

            lot_size = max(min_lot, min(lot_size, max_lot))

            # Round to symbol lot step
            lot_step = symbol_info.volume_step
            lot_size = round(lot_size / lot_step) * lot_step

            logger.info(f"Calculated lot size: {lot_size} (confidence: {confidence}, risk: {risk:.2%})")
            return lot_size

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "_calculate_lot_size"}
                )
            logger.error(f"Error calculating lot size: {str(e)}")
            if hasattr(self.config, 'strategy'):
                return self.config.strategy.lot_size
            else:
                return getattr(self.config, 'lot_size', 0.01)

    def _get_pip_value(self, symbol: str) -> Optional[float]:
        """
        Get the pip value for a symbol.

        Args:
            symbol: Symbol to get pip value for

        Returns:
            Optional[float]: Pip value or None if failed
        """
        try:
            # Get symbol information
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {symbol}")
                return None

            # Calculate pip value based on digits
            digits = symbol_info.digits

            # For Forex: 4 digits = 0.0001, 5 digits = 0.00001, etc.
            if digits == 4 or digits == 5:
                pip_value = 10 ** -4
            # For indices, commodities, crypto: varies
            else:
                pip_value = 10 ** -digits

            return pip_value

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={
                        "method": "_get_pip_value",
                        "symbol": symbol
                    }
                )
            logger.error(f"Error getting pip value: {str(e)}")
            return None

    def get_account_info(self) -> Optional[Any]:
        """
        Get account information.

        Returns:
            Optional[Any]: Account information or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Get account info
            account_info = mt5.account_info()
            if account_info is None:
                error = mt5.last_error()
                logger.error(f"Failed to get account info: {error}")
                return None

            return account_info

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "get_account_info"}
                )
            logger.error(f"Error getting account info: {str(e)}")
            return None

    def update_trade_profits(self) -> None:
        """
        Update profit/loss for all active trades by checking current positions.
        CRITICAL FIX: Improved P&L tracking with better error handling and logging.
        """
        try:
            # Get current positions
            positions = self.get_open_positions()
            if positions is None:
                logger.debug(f"[{self.terminal_id}] No positions returned from MT5")
                return

            logger.debug(f"[{self.terminal_id}] Updating profits for {len(positions)} positions")

            # Update profit for each active trade
            for position in positions:
                ticket = position.ticket
                current_profit = getattr(position, 'profit', 0.0)

                if ticket in self.active_orders:
                    # Update profit in active orders
                    old_profit = self.active_orders[ticket].get('profit_loss', 0.0)
                    self.active_orders[ticket]['profit_loss'] = current_profit

                    # Log significant profit changes
                    if abs(current_profit - old_profit) > 1.0:  # Log changes > $1
                        logger.debug(f"[{self.terminal_id}] Ticket {ticket} P&L updated: {old_profit:.2f} -> {current_profit:.2f}")

                    # Update in trade history
                    for trade in self.trade_history:
                        if isinstance(trade, dict) and trade.get('ticket') == ticket:
                            trade['profit_loss'] = current_profit
                            trade['last_update'] = datetime.now()
                            break
                else:
                    logger.debug(f"[{self.terminal_id}] Position {ticket} not found in active orders")

            # Check for closed positions and update final profit
            self._check_closed_positions()

            # Log current total P&L
            total_pnl = sum(trade.get('profit_loss', 0.0) for trade in self.trade_history)
            logger.debug(f"[{self.terminal_id}] Total P&L across all trades: {total_pnl:.2f}")

            # Save MT5 history to file periodically (every 20th call to avoid excessive I/O)
            if hasattr(self, '_mt5_history_save_count'):
                self._mt5_history_save_count += 1
            else:
                self._mt5_history_save_count = 1

            if self._mt5_history_save_count % 20 == 0:
                self._save_mt5_history_to_file()

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error updating trade profits: {str(e)}")
            import traceback
            logger.debug(f"[{self.terminal_id}] Traceback: {traceback.format_exc()}")

    def _check_closed_positions(self) -> None:
        """
        Check for positions that have been closed and update final profit.
        """
        try:
            # Get history deals for the last day
            from_date = datetime.now() - timedelta(days=1)
            history_deals = mt5.history_deals_get(
                symbol=self.symbol,
                from_date=from_date
            )

            if history_deals is None:
                return

            # Check for closed positions
            for deal in history_deals:
                try:
                    # Safely access deal attributes
                    if hasattr(deal, 'entry') and deal.entry == 1:  # Exit deal (position close)
                        # Use position_id, not order_id (which doesn't exist on TradeDeal)
                        if hasattr(deal, 'position_id'):
                            ticket = deal.position_id
                        else:
                            # Fallback to order attribute if position_id not available
                            ticket = getattr(deal, 'order', None)

                        if ticket and ticket in self.active_orders:
                            # Calculate final profit
                            entry_price = self.active_orders[ticket]["price"]
                            exit_price = getattr(deal, 'price', 0.0)
                            volume = self.active_orders[ticket]["volume"]
                            action = self.active_orders[ticket]["action"]

                            # Calculate profit based on position type
                            if action == "buy":
                                profit = (exit_price - entry_price) * volume * 100  # Adjust for BTCUSD.a
                            else:
                                profit = (entry_price - exit_price) * volume * 100  # Adjust for BTCUSD.a

                            # Update profit/loss tracking
                            self.profit_loss += profit

                            # Update win/loss count
                            if profit > 0:
                                self.winning_trades += 1
                            else:
                                self.losing_trades += 1

                            # Update trade history with final profit
                            for trade in self.trade_history:
                                if isinstance(trade, dict) and trade.get('ticket') == ticket:
                                    trade['profit_loss'] = profit
                                    trade['status'] = 'closed'
                                    trade['close_time'] = datetime.fromtimestamp(getattr(deal, 'time', 0))
                                    trade['close_price'] = exit_price
                                    # Save updated trade to persistent history
                                    self._save_trade_to_history(trade, "closed")
                                    break

                            # Remove from active orders
                            del self.active_orders[ticket]

                            logger.info(f"Position {ticket} closed with profit: {profit:.2f}")

                except Exception as deal_error:
                    logger.warning(f"Error processing deal: {str(deal_error)}")
                    continue

        except Exception as e:
            logger.error(f"Error checking closed positions: {str(e)}")

    def get_trade_history(self, days: int = 7) -> Optional[List]:
        """
        Get trade history for the specified period.

        Args:
            days: Number of days to look back

        Returns:
            Optional[List]: List of completed trades or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Calculate start time
            from_date = datetime.now() - timedelta(days=days)

            # Get history orders
            history_orders = mt5.history_orders_get(
                symbol=self.symbol,
                from_date=from_date
            )

            if history_orders is None:
                error = mt5.last_error()
                logger.error(f"Failed to get history orders: {error}")
                return None

            # Get history deals with safe attribute access
            history_deals = mt5.history_deals_get(
                symbol=self.symbol,
                from_date=from_date
            )

            if history_deals is None:
                error = mt5.last_error()
                logger.error(f"Failed to get history deals: {error}")
                return None

            # Process deals safely to avoid order_id attribute errors
            processed_deals = []
            for deal in history_deals:
                try:
                    # Create a safe dictionary representation of the deal
                    deal_dict = {
                        'ticket': getattr(deal, 'ticket', None),
                        'order': getattr(deal, 'order', None),
                        'position_id': getattr(deal, 'position_id', None),
                        'time': getattr(deal, 'time', None),
                        'type': getattr(deal, 'type', None),
                        'entry': getattr(deal, 'entry', None),
                        'volume': getattr(deal, 'volume', None),
                        'price': getattr(deal, 'price', None),
                        'commission': getattr(deal, 'commission', None),
                        'swap': getattr(deal, 'swap', None),
                        'profit': getattr(deal, 'profit', None),
                        'symbol': getattr(deal, 'symbol', None),
                        'comment': getattr(deal, 'comment', None),
                        'external_id': getattr(deal, 'external_id', None)
                    }
                    processed_deals.append(deal_dict)
                except Exception as deal_error:
                    logger.warning(f"Error processing deal in history: {str(deal_error)}")
                    continue

            return {"orders": history_orders, "deals": processed_deals}

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "get_trade_history"}
                )
            logger.error(f"Error getting position history: {str(e)}")
            return None

    def calculate_position_size(self, account_info) -> float:
        """
        Calculate position size based on account balance and risk management.

        Args:
            account_info: MT5 account information

        Returns:
            float: Position size in lots
        """
        try:
            # Get account balance
            balance = getattr(account_info, 'balance', 10000.0)

            # Calculate risk amount (2% of balance by default)
            risk_percent = 0.02
            risk_amount = balance * risk_percent

            # Get symbol info for contract size
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.warning("Failed to get symbol info, using default lot size")
                return self.min_lot_size

            # Simple position sizing: risk amount / (stop loss in currency units)
            # For BTCUSD.a, use a simple percentage of balance
            position_size = risk_amount / (balance * 0.1)  # 10% of risk amount

            # Ensure within limits
            position_size = max(self.min_lot_size, min(position_size, self.max_lot_size))

            # Round to symbol lot step
            lot_step = getattr(symbol_info, 'volume_step', 0.01)
            position_size = round(position_size / lot_step) * lot_step

            logger.info(f"Calculated position size: {position_size} lots")
            return position_size

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return self.min_lot_size

    def _create_simple_circuit_breaker(self):
        """
        Create a simple circuit breaker implementation.

        Returns:
            Simple circuit breaker object
        """
        class SimpleCircuitBreaker:
            def __init__(self):
                self.failure_count = 0
                self.max_failures = 3
                self.reset_timeout = 60
                self.last_failure_time = 0

            def execute(self, func, circuit_name=None):
                """Execute function with circuit breaker protection."""
                try:
                    # Check if circuit is open
                    current_time = time.time()
                    if (self.failure_count >= self.max_failures and
                        current_time - self.last_failure_time < self.reset_timeout):
                        raise RuntimeError("Circuit breaker is open")

                    # Reset if timeout passed
                    if current_time - self.last_failure_time >= self.reset_timeout:
                        self.failure_count = 0

                    # Execute function
                    result = func()

                    # Reset on success
                    self.failure_count = 0
                    return result

                except Exception as e:
                    # Increment failure count
                    self.failure_count += 1
                    self.last_failure_time = time.time()
                    raise e

        return SimpleCircuitBreaker()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics.
        CRITICAL FIX: Enhanced performance tracking with real-time P&L updates.

        Returns:
            Dict containing performance metrics
        """
        try:
            # First update all trade profits to get current values
            self.update_trade_profits()

            total_trades = len(self.trade_history)
            if total_trades == 0:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0.0,
                    'total_profit': 0.0,
                    'average_profit': 0.0,
                    'max_drawdown': 0.0,
                    'profit_factor': 0.0,
                    'active_positions': len(self.active_orders),
                    'last_update': datetime.now().isoformat()
                }

            # Calculate metrics from current trade data
            profits = []
            for trade in self.trade_history:
                if isinstance(trade, dict):
                    profit = trade.get('profit_loss', 0.0)
                    # Ensure we have a valid profit value
                    if isinstance(profit, (int, float)) and not (math.isnan(profit) or math.isinf(profit)):
                        profits.append(profit)
                    else:
                        profits.append(0.0)

            total_profit = sum(profits)
            winning_trades = sum(1 for p in profits if p > 0)
            losing_trades = sum(1 for p in profits if p < 0)
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

            # Calculate profit factor
            gross_profit = sum(p for p in profits if p > 0)
            gross_loss = abs(sum(p for p in profits if p < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0.0

            # Calculate max drawdown
            running_profit = 0
            peak = 0
            max_drawdown = 0
            for profit in profits:
                running_profit += profit
                if running_profit > peak:
                    peak = running_profit
                drawdown = peak - running_profit
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            # Calculate additional metrics
            average_win = gross_profit / winning_trades if winning_trades > 0 else 0
            average_loss = gross_loss / losing_trades if losing_trades > 0 else 0

            # Get current active positions profit
            active_profit = sum(trade.get('profit_loss', 0.0) for trade in self.active_orders.values())

            metrics = {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 2),
                'total_profit': round(total_profit, 2),
                'average_profit': round(total_profit / total_trades if total_trades > 0 else 0, 2),
                'max_drawdown': round(max_drawdown, 2),
                'profit_factor': round(profit_factor, 2),
                'gross_profit': round(gross_profit, 2),
                'gross_loss': round(gross_loss, 2),
                'average_win': round(average_win, 2),
                'average_loss': round(average_loss, 2),
                'active_positions': len(self.active_orders),
                'active_profit': round(active_profit, 2),
                'last_update': datetime.now().isoformat(),
                'terminal_id': self.terminal_id
            }

            # Log performance summary periodically
            if hasattr(self, '_last_metrics_log'):
                if (datetime.now() - self._last_metrics_log).seconds > 300:  # Every 5 minutes
                    logger.info(f"[{self.terminal_id}] Performance: {total_trades} trades, {win_rate:.1f}% win rate, ${total_profit:.2f} total P&L")
                    self._last_metrics_log = datetime.now()
            else:
                self._last_metrics_log = datetime.now()

            # Save performance metrics to persistent history
            self._save_performance_to_history()

            return metrics

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error calculating performance metrics: {str(e)}")
            import traceback
            logger.debug(f"[{self.terminal_id}] Traceback: {traceback.format_exc()}")
            return {
                'total_trades': 0,
                'error': str(e),
                'last_update': datetime.now().isoformat(),
                'terminal_id': self.terminal_id
            }