#!/usr/bin/env python3
"""
Check Account Balances for All MT5 Terminals

This script checks the account balance and status for all configured MT5 terminals
to identify which accounts have insufficient funds.
"""

import MetaTrader5 as mt5
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config():
    """Load terminal configurations from config.json"""
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        return config['mt5']['terminals']
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return {}

def check_terminal_balance(terminal_id, terminal_config):
    """Check balance and account info for a specific terminal"""
    logger.info(f"\n{'='*50}")
    logger.info(f"CHECKING TERMINAL {terminal_id}")
    logger.info(f"{'='*50}")
    
    try:
        # Initialize MT5 connection
        logger.info(f"Connecting to terminal {terminal_id}...")
        logger.info(f"Path: {terminal_config['path']}")
        logger.info(f"Server: {terminal_config['server']}")
        logger.info(f"Login: {terminal_config['login']}")
        
        # Connect to MT5
        if not mt5.initialize(
            path=terminal_config["path"],
            login=int(terminal_config["login"]),
            password=terminal_config["password"],
            server=terminal_config["server"],
            portable=True
        ):
            error = mt5.last_error()
            logger.error(f"❌ FAILED to connect to terminal {terminal_id}: {error}")
            return False
        
        # Get account info
        account_info = mt5.account_info()
        if not account_info:
            logger.error(f"❌ Could not get account info for terminal {terminal_id}")
            return False
        
        # Get terminal info
        terminal_info = mt5.terminal_info()
        
        # Display account information
        logger.info(f"✅ CONNECTED to terminal {terminal_id}")
        logger.info(f"Account Login: {account_info.login}")
        logger.info(f"Account Name: {account_info.name}")
        logger.info(f"Account Server: {account_info.server}")
        logger.info(f"Account Currency: {account_info.currency}")
        logger.info(f"Account Balance: ${account_info.balance:,.2f}")
        logger.info(f"Account Equity: ${account_info.equity:,.2f}")
        logger.info(f"Account Margin: ${account_info.margin:,.2f}")
        logger.info(f"Free Margin: ${account_info.margin_free:,.2f}")
        logger.info(f"Margin Level: {account_info.margin_level:.2f}%")
        
        # Check trading permissions
        if terminal_info:
            logger.info(f"Algo Trading Enabled: {'✅ YES' if terminal_info.trade_allowed else '❌ NO'}")
        
        # Check if account can trade
        if account_info.balance <= 0:
            logger.error(f"❌ INSUFFICIENT BALANCE: ${account_info.balance:,.2f}")
            return False
        elif account_info.margin_free <= 0:
            logger.error(f"❌ NO FREE MARGIN: ${account_info.margin_free:,.2f}")
            return False
        else:
            logger.info(f"✅ ACCOUNT READY FOR TRADING")
            return True
            
    except Exception as e:
        logger.error(f"❌ ERROR checking terminal {terminal_id}: {e}")
        return False

def main():
    """Main function to check all terminal balances"""
    logger.info("🔍 CHECKING ALL MT5 TERMINAL ACCOUNT BALANCES")
    logger.info(f"Timestamp: {datetime.now()}")
    
    # Load terminal configurations
    terminals = load_config()
    if not terminals:
        logger.error("No terminal configurations found!")
        return
    
    results = {}
    
    # Check each terminal
    for terminal_id, terminal_config in terminals.items():
        try:
            success = check_terminal_balance(terminal_id, terminal_config)
            results[terminal_id] = success
        except Exception as e:
            logger.error(f"Error checking terminal {terminal_id}: {e}")
            results[terminal_id] = False
        finally:
            # Don't call mt5.shutdown() to preserve algo trading
            pass
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📊 SUMMARY OF ALL TERMINALS")
    logger.info(f"{'='*60}")
    
    working_terminals = []
    failed_terminals = []
    
    for terminal_id, success in results.items():
        if success:
            logger.info(f"✅ Terminal {terminal_id}: READY FOR TRADING")
            working_terminals.append(terminal_id)
        else:
            logger.error(f"❌ Terminal {terminal_id}: CANNOT TRADE")
            failed_terminals.append(terminal_id)
    
    logger.info(f"\n📈 Working Terminals: {len(working_terminals)} ({', '.join(working_terminals)})")
    logger.error(f"💸 Failed Terminals: {len(failed_terminals)} ({', '.join(failed_terminals)})")
    
    if failed_terminals:
        logger.error(f"\n🚨 ACTION REQUIRED:")
        logger.error(f"The following terminals need new demo accounts or balance reset:")
        for terminal_id in failed_terminals:
            logger.error(f"  - Terminal {terminal_id}")
        logger.error(f"\nTo fix this:")
        logger.error(f"1. Open each failed MT5 terminal")
        logger.error(f"2. Request new demo account or balance reset")
        logger.error(f"3. Update config.json with new login credentials")

if __name__ == "__main__":
    main()
