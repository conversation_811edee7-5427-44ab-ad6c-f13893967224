#!/usr/bin/env python3
"""
Trading History Validator

This module provides validation and integrity checks for trading history data
to ensure all trades, signals, and performance data are properly saved and retrievable.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd

# Use comprehensive logging system
try:
    from utils.comprehensive_logging import get_main_logger, log_trading_history
    logger = get_main_logger()
except ImportError:
    # Fallback to basic logging if comprehensive logging not available
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class TradingHistoryValidator:
    """Validates trading history data integrity and completeness."""
    
    def __init__(self, trading_history_base_dir: str = "logs/trading_history"):
        self.base_dir = Path(trading_history_base_dir)
        self.validation_results = {}
        
    def validate_all_terminals(self) -> Dict[str, Any]:
        """Validate trading history for all terminals."""
        logger.info("Starting comprehensive trading history validation...")
        
        overall_results = {
            "validation_timestamp": datetime.now().isoformat(),
            "terminals_validated": 0,
            "terminals_with_issues": 0,
            "total_issues_found": 0,
            "terminal_results": {},
            "consolidated_results": {},
            "recommendations": []
        }
        
        # Validate each terminal
        for terminal_id in range(1, 6):
            terminal_results = self.validate_terminal_history(terminal_id)
            overall_results["terminal_results"][f"terminal_{terminal_id}"] = terminal_results
            overall_results["terminals_validated"] += 1
            
            if terminal_results["issues_found"] > 0:
                overall_results["terminals_with_issues"] += 1
                overall_results["total_issues_found"] += terminal_results["issues_found"]
        
        # Validate consolidated history
        consolidated_results = self.validate_consolidated_history()
        overall_results["consolidated_results"] = consolidated_results
        
        if consolidated_results["issues_found"] > 0:
            overall_results["total_issues_found"] += consolidated_results["issues_found"]
        
        # Generate recommendations
        overall_results["recommendations"] = self._generate_recommendations(overall_results)
        
        # Save validation report
        self._save_validation_report(overall_results)
        
        logger.info(f"Trading history validation completed. Found {overall_results['total_issues_found']} issues across {overall_results['terminals_with_issues']} terminals.")
        
        return overall_results
    
    def validate_terminal_history(self, terminal_id: int) -> Dict[str, Any]:
        """Validate trading history for a specific terminal."""
        terminal_dir = self.base_dir / f"terminal_{terminal_id}"
        
        results = {
            "terminal_id": terminal_id,
            "validation_timestamp": datetime.now().isoformat(),
            "directory_exists": terminal_dir.exists(),
            "subdirectories_check": {},
            "file_integrity_check": {},
            "data_consistency_check": {},
            "issues_found": 0,
            "issues_details": []
        }
        
        if not terminal_dir.exists():
            results["issues_found"] += 1
            results["issues_details"].append(f"Terminal {terminal_id} directory does not exist")
            return results
        
        # Check required subdirectories
        required_subdirs = ["trades", "signals", "performance", "mt5_history"]
        for subdir in required_subdirs:
            subdir_path = terminal_dir / subdir
            results["subdirectories_check"][subdir] = subdir_path.exists()
            if not subdir_path.exists():
                results["issues_found"] += 1
                results["issues_details"].append(f"Missing {subdir} subdirectory for terminal {terminal_id}")
        
        # Validate file integrity
        results["file_integrity_check"] = self._validate_terminal_files(terminal_dir)
        results["issues_found"] += results["file_integrity_check"]["issues_count"]
        results["issues_details"].extend(results["file_integrity_check"]["issues"])
        
        # Validate data consistency
        results["data_consistency_check"] = self._validate_terminal_data_consistency(terminal_dir)
        results["issues_found"] += results["data_consistency_check"]["issues_count"]
        results["issues_details"].extend(results["data_consistency_check"]["issues"])
        
        return results
    
    def validate_consolidated_history(self) -> Dict[str, Any]:
        """Validate consolidated trading history."""
        consolidated_dir = self.base_dir / "consolidated_mt5"
        
        results = {
            "validation_timestamp": datetime.now().isoformat(),
            "directory_exists": consolidated_dir.exists(),
            "files_check": {},
            "data_integrity_check": {},
            "issues_found": 0,
            "issues_details": []
        }
        
        if not consolidated_dir.exists():
            results["issues_found"] += 1
            results["issues_details"].append("Consolidated MT5 history directory does not exist")
            return results
        
        # Check required consolidated files
        today = datetime.now().strftime('%Y%m%d')
        required_files = [
            f"all_orders_{today}.json",
            f"all_deals_{today}.json",
            f"all_positions_{today}.json"
        ]
        
        for filename in required_files:
            file_path = consolidated_dir / filename
            results["files_check"][filename] = {
                "exists": file_path.exists(),
                "size_bytes": file_path.stat().st_size if file_path.exists() else 0,
                "last_modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat() if file_path.exists() else None
            }
            
            if not file_path.exists():
                results["issues_found"] += 1
                results["issues_details"].append(f"Missing consolidated file: {filename}")
        
        return results
    
    def _validate_terminal_files(self, terminal_dir: Path) -> Dict[str, Any]:
        """Validate file integrity for a terminal."""
        results = {
            "files_checked": 0,
            "files_valid": 0,
            "files_invalid": 0,
            "issues_count": 0,
            "issues": [],
            "file_details": {}
        }
        
        # Check all JSON files in terminal directory
        for json_file in terminal_dir.rglob("*.json"):
            results["files_checked"] += 1
            file_result = self._validate_json_file(json_file)
            results["file_details"][str(json_file.relative_to(terminal_dir))] = file_result
            
            if file_result["valid"]:
                results["files_valid"] += 1
            else:
                results["files_invalid"] += 1
                results["issues_count"] += 1
                results["issues"].append(f"Invalid JSON file: {json_file.relative_to(terminal_dir)} - {file_result['error']}")
        
        return results
    
    def _validate_json_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate a JSON file."""
        result = {
            "valid": False,
            "size_bytes": 0,
            "last_modified": None,
            "records_count": 0,
            "error": None
        }
        
        try:
            if file_path.exists():
                result["size_bytes"] = file_path.stat().st_size
                result["last_modified"] = datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                
                if result["size_bytes"] > 0:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        
                    if isinstance(data, list):
                        result["records_count"] = len(data)
                    elif isinstance(data, dict):
                        result["records_count"] = 1
                        # Check for nested arrays
                        for key, value in data.items():
                            if isinstance(value, list):
                                result["records_count"] += len(value)
                    
                    result["valid"] = True
                else:
                    result["error"] = "File is empty"
            else:
                result["error"] = "File does not exist"
                
        except json.JSONDecodeError as e:
            result["error"] = f"JSON decode error: {str(e)}"
        except Exception as e:
            result["error"] = f"Unexpected error: {str(e)}"
        
        return result
    
    def _validate_terminal_data_consistency(self, terminal_dir: Path) -> Dict[str, Any]:
        """Validate data consistency for a terminal."""
        results = {
            "issues_count": 0,
            "issues": [],
            "trade_signal_consistency": {},
            "timestamp_consistency": {},
            "data_completeness": {}
        }
        
        # This is a placeholder for more complex data consistency checks
        # In a full implementation, you would:
        # 1. Check that trade records match signal records
        # 2. Verify timestamp consistency across files
        # 3. Ensure data completeness (no missing time periods)
        # 4. Cross-reference with MT5 native history
        
        return results
    
    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        if validation_results["total_issues_found"] == 0:
            recommendations.append("✓ All trading history validation checks passed successfully")
        else:
            recommendations.append(f"⚠ Found {validation_results['total_issues_found']} issues that need attention")
            
            if validation_results["terminals_with_issues"] > 0:
                recommendations.append(f"• {validation_results['terminals_with_issues']} terminals have history issues")
                recommendations.append("• Review individual terminal validation results for details")
            
            recommendations.append("• Consider running history repair/recovery procedures")
            recommendations.append("• Verify MT5 connections and trading bot configurations")
        
        return recommendations
    
    def _save_validation_report(self, results: Dict[str, Any]) -> None:
        """Save validation report to file."""
        try:
            reports_dir = self.base_dir / "validation_reports"
            reports_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = reports_dir / f"trading_history_validation_{timestamp}.json"
            
            with open(report_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Trading history validation report saved to: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save validation report: {e}")

def validate_trading_history() -> Dict[str, Any]:
    """Convenience function to validate all trading history."""
    validator = TradingHistoryValidator()
    return validator.validate_all_terminals()

if __name__ == "__main__":
    # Run validation when script is executed directly
    results = validate_trading_history()
    
    print("\n" + "="*60)
    print("TRADING HISTORY VALIDATION RESULTS")
    print("="*60)
    print(f"Terminals validated: {results['terminals_validated']}")
    print(f"Terminals with issues: {results['terminals_with_issues']}")
    print(f"Total issues found: {results['total_issues_found']}")
    print("\nRecommendations:")
    for rec in results['recommendations']:
        print(f"  {rec}")
    print("="*60)
