#!/usr/bin/env python3
"""
Test Trading History Logging Functionality

This script thoroughly tests the trading history logging system to ensure
all trades, signals, and performance data are properly saved and retrievable.
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingHistoryLoggingTester:
    """Tests trading history logging functionality."""
    
    def __init__(self):
        self.test_results = {
            "test_timestamp": datetime.now().isoformat(),
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "test_details": {},
            "overall_status": "UNKNOWN"
        }
        
        # Setup test environment
        self.trading_history_dir = Path("logs/trading_history")
        self.test_terminal_id = "1"
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all trading history logging tests."""
        logger.info("Starting comprehensive trading history logging tests...")
        
        # Test 1: Directory structure creation
        self._test_directory_structure()
        
        # Test 2: Comprehensive logging system
        self._test_comprehensive_logging()
        
        # Test 3: Independent trading bot logging
        self._test_independent_bot_logging()
        
        # Test 4: Trading executor logging
        self._test_trading_executor_logging()
        
        # Test 5: Signal generator logging
        self._test_signal_generator_logging()
        
        # Test 6: MT5 connection logging
        self._test_mt5_connection_logging()
        
        # Test 7: Main bot coordination
        self._test_main_bot_coordination()
        
        # Test 8: Data persistence and retrieval
        self._test_data_persistence()
        
        # Test 9: Validation and reconciliation
        self._test_validation_reconciliation()
        
        # Calculate overall results
        self._calculate_overall_results()
        
        # Save test report
        self._save_test_report()
        
        logger.info(f"Trading history logging tests completed: {self.test_results['tests_passed']}/{self.test_results['tests_run']} passed")
        
        return self.test_results
    
    def _test_directory_structure(self):
        """Test that directory structure is properly created."""
        test_name = "directory_structure"
        self.test_results["tests_run"] += 1
        
        try:
            # Check main trading history directory
            if not self.trading_history_dir.exists():
                self.trading_history_dir.mkdir(parents=True, exist_ok=True)
            
            # Check terminal directories
            terminal_dirs_exist = True
            for terminal_id in range(1, 6):
                terminal_dir = self.trading_history_dir / f"terminal_{terminal_id}"
                if not terminal_dir.exists():
                    terminal_dir.mkdir(exist_ok=True)
                
                # Check subdirectories
                for subdir in ["trades", "signals", "performance", "mt5_history"]:
                    subdir_path = terminal_dir / subdir
                    if not subdir_path.exists():
                        subdir_path.mkdir(exist_ok=True)
            
            # Check consolidated directory
            consolidated_dir = self.trading_history_dir / "consolidated_mt5"
            if not consolidated_dir.exists():
                consolidated_dir.mkdir(exist_ok=True)
            
            self.test_results["tests_passed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "PASSED",
                "message": "Directory structure created successfully"
            }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Directory structure creation failed: {str(e)}"
            }
    
    def _test_comprehensive_logging(self):
        """Test comprehensive logging system."""
        test_name = "comprehensive_logging"
        self.test_results["tests_run"] += 1
        
        try:
            from utils.comprehensive_logging import (
                get_trading_history_logger,
                get_consolidated_trading_history_logger,
                log_trading_history
            )
            
            # Test terminal-specific logger
            terminal_logger = get_trading_history_logger(self.test_terminal_id)
            terminal_logger.info("Test trading history log entry")
            
            # Test consolidated logger
            consolidated_logger = get_consolidated_trading_history_logger()
            consolidated_logger.info("Test consolidated trading history log entry")
            
            # Test convenience function
            log_trading_history(self.test_terminal_id, "Test convenience function log entry")
            
            self.test_results["tests_passed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "PASSED",
                "message": "Comprehensive logging system working correctly"
            }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Comprehensive logging test failed: {str(e)}"
            }
    
    def _test_independent_bot_logging(self):
        """Test independent trading bot logging."""
        test_name = "independent_bot_logging"
        self.test_results["tests_run"] += 1
        
        try:
            # Test that independent bot logging functions exist and can be called
            # Note: We can't easily test the full bot without MT5 connection
            
            # Check if the logging methods exist in the independent bot
            from independent_trading_bot import IndependentTradingBot
            
            # Verify the bot has the required logging methods
            required_methods = [
                '_setup_trading_history_persistence',
                '_log_signal_to_history',
                '_log_trade_to_history',
                '_log_performance_to_history',
                '_log_mt5_history_to_file'
            ]
            
            methods_exist = True
            for method_name in required_methods:
                if not hasattr(IndependentTradingBot, method_name):
                    methods_exist = False
                    break
            
            if methods_exist:
                self.test_results["tests_passed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "message": "Independent bot logging methods exist"
                }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "Independent bot missing required logging methods"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Independent bot logging test failed: {str(e)}"
            }
    
    def _test_trading_executor_logging(self):
        """Test trading executor logging."""
        test_name = "trading_executor_logging"
        self.test_results["tests_run"] += 1
        
        try:
            from trading.executor import TradeExecutor
            
            # Verify the executor has the required logging methods
            required_methods = [
                '_setup_trading_history_persistence',
                '_save_trade_to_history',
                '_save_performance_to_history',
                '_save_mt5_history_to_file'
            ]
            
            methods_exist = True
            for method_name in required_methods:
                if not hasattr(TradeExecutor, method_name):
                    methods_exist = False
                    break
            
            if methods_exist:
                self.test_results["tests_passed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "message": "Trading executor logging methods exist"
                }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "Trading executor missing required logging methods"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Trading executor logging test failed: {str(e)}"
            }
    
    def _test_signal_generator_logging(self):
        """Test signal generator logging."""
        test_name = "signal_generator_logging"
        self.test_results["tests_run"] += 1
        
        try:
            from trading.signal_generator import SignalGenerator
            
            # Verify the signal generator has the required logging methods
            required_methods = [
                '_setup_signal_history_persistence',
                '_save_signal_to_history'
            ]
            
            methods_exist = True
            for method_name in required_methods:
                if not hasattr(SignalGenerator, method_name):
                    methods_exist = False
                    break
            
            if methods_exist:
                self.test_results["tests_passed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "message": "Signal generator logging methods exist"
                }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "Signal generator missing required logging methods"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Signal generator logging test failed: {str(e)}"
            }
    
    def _test_mt5_connection_logging(self):
        """Test MT5 connection logging."""
        test_name = "mt5_connection_logging"
        self.test_results["tests_run"] += 1
        
        try:
            from utils.mt5.multi_mt5_connection import MT5MultiConnector
            
            # Verify the MT5 connector has the required logging methods
            required_methods = [
                '_setup_trading_history_persistence',
                '_save_terminal_history_to_file',
                '_save_consolidated_history_to_file',
                'get_all_terminals_history'
            ]
            
            methods_exist = True
            for method_name in required_methods:
                if not hasattr(MT5MultiConnector, method_name):
                    methods_exist = False
                    break
            
            if methods_exist:
                self.test_results["tests_passed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "message": "MT5 connection logging methods exist"
                }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "MT5 connection missing required logging methods"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"MT5 connection logging test failed: {str(e)}"
            }
    
    def _test_main_bot_coordination(self):
        """Test main bot coordination of trading history."""
        test_name = "main_bot_coordination"
        self.test_results["tests_run"] += 1
        
        try:
            from trading.bot import TradingBot
            
            # Verify the trading bot has the required logging methods
            required_methods = [
                '_setup_trading_history_persistence',
                '_save_trade_to_history',
                '_save_signal_to_history',
                '_save_performance_to_history',
                '_save_mt5_history_to_file'
            ]
            
            methods_exist = True
            for method_name in required_methods:
                if not hasattr(TradingBot, method_name):
                    methods_exist = False
                    break
            
            if methods_exist:
                self.test_results["tests_passed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "message": "Main trading bot logging methods exist"
                }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "Main trading bot missing required logging methods"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Main bot coordination test failed: {str(e)}"
            }
    
    def _test_data_persistence(self):
        """Test data persistence and file creation."""
        test_name = "data_persistence"
        self.test_results["tests_run"] += 1
        
        try:
            # Create test data files
            test_data = {
                "timestamp": datetime.now().isoformat(),
                "test_data": True,
                "terminal_id": self.test_terminal_id
            }
            
            # Test writing to different file types
            terminal_dir = self.trading_history_dir / f"terminal_{self.test_terminal_id}"
            
            # Test trades file
            trades_file = terminal_dir / "trades" / "test_trades.json"
            with open(trades_file, 'w') as f:
                json.dump([test_data], f, indent=2, default=str)
            
            # Test signals file
            signals_file = terminal_dir / "signals" / "test_signals.json"
            with open(signals_file, 'w') as f:
                json.dump([test_data], f, indent=2, default=str)
            
            # Verify files were created and can be read
            files_created = trades_file.exists() and signals_file.exists()
            
            if files_created:
                # Test reading the data back
                with open(trades_file, 'r') as f:
                    read_data = json.load(f)
                
                data_valid = len(read_data) == 1 and read_data[0]["test_data"] is True
                
                if data_valid:
                    self.test_results["tests_passed"] += 1
                    self.test_results["test_details"][test_name] = {
                        "status": "PASSED",
                        "message": "Data persistence working correctly"
                    }
                else:
                    self.test_results["tests_failed"] += 1
                    self.test_results["test_details"][test_name] = {
                        "status": "FAILED",
                        "message": "Data persistence - data corruption detected"
                    }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "Data persistence - files not created"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Data persistence test failed: {str(e)}"
            }
    
    def _test_validation_reconciliation(self):
        """Test validation and reconciliation functionality."""
        test_name = "validation_reconciliation"
        self.test_results["tests_run"] += 1
        
        try:
            from utils.trading_history_validator import TradingHistoryValidator
            from utils.trading_history_reconciler import TradingHistoryReconciler
            
            # Test validator
            validator = TradingHistoryValidator()
            validation_results = validator.validate_all_terminals()
            
            # Test reconciler
            reconciler = TradingHistoryReconciler()
            reconciliation_results = reconciler.reconcile_all_terminals(days_back=1)
            
            # Check if both completed without errors
            validation_completed = "validation_timestamp" in validation_results
            reconciliation_completed = "reconciliation_timestamp" in reconciliation_results
            
            if validation_completed and reconciliation_completed:
                self.test_results["tests_passed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "message": "Validation and reconciliation working correctly"
                }
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "message": "Validation or reconciliation failed to complete"
                }
            
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "message": f"Validation/reconciliation test failed: {str(e)}"
            }
    
    def _calculate_overall_results(self):
        """Calculate overall test results."""
        if self.test_results["tests_failed"] == 0:
            self.test_results["overall_status"] = "PASSED"
        elif self.test_results["tests_passed"] > self.test_results["tests_failed"]:
            self.test_results["overall_status"] = "MOSTLY_PASSED"
        else:
            self.test_results["overall_status"] = "FAILED"
    
    def _save_test_report(self):
        """Save test report to file."""
        try:
            reports_dir = self.trading_history_dir / "test_reports"
            reports_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = reports_dir / f"trading_history_logging_test_{timestamp}.json"
            
            with open(report_file, 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            
            logger.info(f"Test report saved to: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")

def test_trading_history_logging() -> Dict[str, Any]:
    """Convenience function to test trading history logging."""
    tester = TradingHistoryLoggingTester()
    return tester.run_all_tests()

if __name__ == "__main__":
    # Run tests when script is executed directly
    results = test_trading_history_logging()
    
    print("\n" + "="*60)
    print("TRADING HISTORY LOGGING TEST RESULTS")
    print("="*60)
    print(f"Tests run: {results['tests_run']}")
    print(f"Tests passed: {results['tests_passed']}")
    print(f"Tests failed: {results['tests_failed']}")
    print(f"Overall status: {results['overall_status']}")
    print("\nTest Details:")
    for test_name, details in results['test_details'].items():
        status_symbol = "✓" if details['status'] == "PASSED" else "✗"
        print(f"  {status_symbol} {test_name}: {details['message']}")
    print("="*60)
