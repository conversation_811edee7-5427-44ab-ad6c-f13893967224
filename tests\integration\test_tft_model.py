"""
Test script for the updated TFT model implementation.
This script builds, trains, and evaluates the TFT model on a small dataset.
"""
import logging
import numpy as np
import pandas as pd
import os
from pathlib import Path
import matplotlib.pyplot as plt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_tft_model.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress TensorFlow warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Import the TFT model
from models.tft_model import TFTModel

def create_synthetic_data(n_samples=1000, sequence_length=60, n_features=5):
    """
    Create synthetic data for testing the TFT model.
    
    Args:
        n_samples: Number of samples
        sequence_length: Length of each sequence
        n_features: Number of features
        
    Returns:
        X_train, y_train, X_val, y_val
    """
    logger.info(f"Creating synthetic data with {n_samples} samples, sequence length {sequence_length}, and {n_features} features")
    
    # Create time series data with some patterns
    time = np.arange(n_samples + sequence_length)
    
    # Create features with different patterns
    features = []
    for i in range(n_features):
        # Create a feature with sine wave pattern and some noise
        feature = np.sin(time * (0.01 + i * 0.005)) + np.random.normal(0, 0.1, len(time))
        features.append(feature)
    
    # Stack features into a 2D array
    data = np.column_stack(features)
    
    # Create sequences
    X = []
    y = []
    
    for i in range(n_samples):
        X.append(data[i:i+sequence_length])
        # Target is the next value of the first feature
        y.append(data[i+sequence_length, 0])
    
    X = np.array(X)
    y = np.array(y)
    
    # Split into train and validation sets (80/20)
    split_idx = int(n_samples * 0.8)
    X_train, X_val = X[:split_idx], X[split_idx:]
    y_train, y_val = y[:split_idx], y[split_idx:]
    
    logger.info(f"Created data with shapes: X_train={X_train.shape}, y_train={y_train.shape}, X_val={X_val.shape}, y_val={y_val.shape}")
    
    return X_train, y_train, X_val, y_val

def test_tft_model():
    """
    Test the TFT model implementation.
    """
    logger.info("Starting TFT model test")
    
    # Create synthetic data
    X_train, y_train, X_val, y_val = create_synthetic_data(
        n_samples=1000, 
        sequence_length=60, 
        n_features=5
    )
    
    # Create model configuration
    model_config = {
        'model_name': 'tft_test',
        'timeframe': 'M5',
        'terminal_id': '1',
        'symbol': 'BTCUSD.a',
        'input_dim': X_train.shape[2],  # Number of features
        'output_dim': 1,
        'sequence_length': X_train.shape[1],  # Sequence length
        'hidden_size': 64,
        'attention_head_size': 4,
        'dropout_rate': 0.1,
        'num_lstm_layers': 2,
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 10,  # Small number for testing
        'patience': 5,
        'models_base_path': 'models/test'
    }
    
    # Create output directory
    output_dir = Path('models/test/M5')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize model
    logger.info("Initializing TFT model")
    model = TFTModel(model_config)
    
    # Build model
    logger.info("Building TFT model")
    model.build()
    
    # Train model
    logger.info("Training TFT model")
    history = model.train(X_train, y_train, validation_data=(X_val, y_val))
    
    # Evaluate model
    logger.info("Evaluating TFT model")
    metrics = model.evaluate(X_val, y_val)
    logger.info(f"Evaluation metrics: {metrics}")
    
    # Make predictions
    logger.info("Making predictions with TFT model")
    y_pred = model.predict(X_val)
    
    # Plot predictions vs actual
    plt.figure(figsize=(12, 6))
    plt.plot(y_val[:100], label='Actual')
    plt.plot(y_pred[:100], label='Predicted')
    plt.legend()
    plt.title('TFT Model Predictions vs Actual')
    plt.savefig('test_tft_predictions.png')
    plt.close()
    
    # Save model
    logger.info("Saving TFT model")
    model.save()
    
    # Load model and make predictions again to verify
    logger.info("Loading TFT model and making predictions again")
    loaded_model = TFTModel(model_config)
    # CRITICAL FIX: Use load() method which has fallback to build() when files are missing
    if hasattr(loaded_model, 'load'):
        loaded_model.load()
    else:
        logger.warning(f"Model has no load() method")
    y_pred_loaded = loaded_model.predict(X_val)
    
    # Verify predictions are the same
    is_same = np.allclose(y_pred, y_pred_loaded, rtol=1e-5, atol=1e-5)
    logger.info(f"Predictions from loaded model match original: {is_same}")
    
    # Test sequence prediction
    logger.info("Testing sequence prediction")
    seq_pred = model.predict_sequence(X_val[0:1], prediction_length=10)
    logger.info(f"Sequence prediction shape: {seq_pred.shape}")
    
    # Try visualizing attention weights
    logger.info("Testing attention visualization")
    try:
        model.visualize_attention(X_val[:3], save_path='test_tft_attention.png')
        logger.info("Attention visualization successful")
    except Exception as e:
        logger.warning(f"Attention visualization failed: {e}")
    
    logger.info("TFT model test completed successfully")
    return model, history, metrics

if __name__ == "__main__":
    test_tft_model()
