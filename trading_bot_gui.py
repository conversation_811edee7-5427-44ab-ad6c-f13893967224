"""
Trading Bot GUI - A comprehensive interface for the trading bot.
This GUI consolidates all functions and visualizations in one place.
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from pathlib import Path
from datetime import datetime, timedelta
import os
import subprocess
import threading
import time
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot_gui.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingBotGUI:
    """Main GUI class for the Trading Bot application."""

    def __init__(self, root):
        """Initialize the GUI.

        Args:
            root: The tkinter root window
        """
        self.root = root
        self.root.title("Trading Bot GUI")
        self.root.geometry("1200x800")

        # Set up the main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Create a notebook (tabbed interface)
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.create_data_collection_tab()
        self.create_data_tab()
        self.create_model_tab()
        self.create_trading_tab()
        self.create_visualization_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        logger.info("Trading Bot GUI initialized")

    def create_data_collection_tab(self):
        """Create the Data Collection tab for collecting and verifying historical data from MT5 terminals."""
        collection_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(collection_frame, text="Data Collection")

        # Add a label
        ttk.Label(collection_frame, text="MT5 Data Collection", font=("Arial", 16)).pack(pady=10)

        # Create a frame for terminal selection
        terminal_frame = ttk.LabelFrame(collection_frame, text="Terminal Selection")
        terminal_frame.pack(fill=tk.X, pady=5)

        # Terminal selection
        self.terminal_vars = {}
        for i in range(1, 6):  # 5 terminals
            var = tk.BooleanVar(value=True)
            self.terminal_vars[i] = var
            ttk.Checkbutton(terminal_frame, text=f"Terminal {i}", variable=var).grid(
                row=0, column=i-1, padx=10, pady=5, sticky=tk.W)

        # Create a frame for symbol and timeframe selection
        symbol_frame = ttk.LabelFrame(collection_frame, text="Symbol & Timeframe Selection")
        symbol_frame.pack(fill=tk.X, pady=5)

        # Symbol selection
        ttk.Label(symbol_frame, text="Symbol:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.collection_symbol_var = tk.StringVar(value="BTCUSD.a")
        symbol_entry = ttk.Entry(symbol_frame, textvariable=self.collection_symbol_var, width=15)
        symbol_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # Timeframe selection
        self.timeframe_vars = {}
        timeframes = ["M5", "M15", "M30", "H1", "H4"]
        for i, tf in enumerate(timeframes):
            var = tk.BooleanVar(value=True)
            self.timeframe_vars[tf] = var
            ttk.Checkbutton(symbol_frame, text=tf, variable=var).grid(
                row=0, column=i+2, padx=10, pady=5, sticky=tk.W)

        # Create a frame for date range selection
        date_frame = ttk.LabelFrame(collection_frame, text="Date Range")
        date_frame.pack(fill=tk.X, pady=5)

        # Start date
        ttk.Label(date_frame, text="Start Date:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.collection_start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d"))
        start_date_entry = ttk.Entry(date_frame, textvariable=self.collection_start_date_var, width=15)
        start_date_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # End date
        ttk.Label(date_frame, text="End Date:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.collection_end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        end_date_entry = ttk.Entry(date_frame, textvariable=self.collection_end_date_var, width=15)
        end_date_entry.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)

        # Create a frame for preprocessing options
        preprocess_frame = ttk.LabelFrame(collection_frame, text="Preprocessing Options")
        preprocess_frame.pack(fill=tk.X, pady=5)

        # Technical indicators
        self.preprocess_vars = {}

        # Row 1: Common indicators
        ttk.Label(preprocess_frame, text="Technical Indicators:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)

        indicators = [
            ("RSI", "rsi"),
            ("MACD", "macd"),
            ("Bollinger Bands", "bb"),
            ("ATR", "atr"),
            ("EMA", "ema")
        ]

        for i, (label, var_name) in enumerate(indicators):
            var = tk.BooleanVar(value=False)
            self.preprocess_vars[var_name] = var
            ttk.Checkbutton(preprocess_frame, text=label, variable=var).grid(
                row=0, column=i+1, padx=10, pady=5, sticky=tk.W)

        # Row 2: Additional options
        ttk.Label(preprocess_frame, text="Additional Options:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)

        # Fill missing values option
        self.fill_missing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(preprocess_frame, text="Fill Missing Values", variable=self.fill_missing_var).grid(
            row=1, column=1, padx=10, pady=5, sticky=tk.W)

        # Remove outliers option
        self.remove_outliers_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(preprocess_frame, text="Remove Outliers", variable=self.remove_outliers_var).grid(
            row=1, column=2, padx=10, pady=5, sticky=tk.W)

        # Normalize data option
        self.normalize_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(preprocess_frame, text="Normalize Data", variable=self.normalize_var).grid(
            row=1, column=3, padx=10, pady=5, sticky=tk.W)

        # Add custom features option
        self.custom_features_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(preprocess_frame, text="Add Custom Features", variable=self.custom_features_var).grid(
            row=1, column=4, padx=10, pady=5, sticky=tk.W)

        # Create a frame for scheduling
        schedule_frame = ttk.LabelFrame(collection_frame, text="Scheduled Collection")
        schedule_frame.pack(fill=tk.X, pady=5)

        # Row 1: Schedule options
        ttk.Label(schedule_frame, text="Schedule:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)

        # Schedule interval
        self.schedule_interval_var = tk.StringVar(value="1h")
        ttk.Label(schedule_frame, text="Interval:").grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        interval_combo = ttk.Combobox(schedule_frame, textvariable=self.schedule_interval_var,
                                     values=["5m", "15m", "30m", "1h", "4h", "12h", "24h"], width=5)
        interval_combo.grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)

        # Auto-combine option
        self.auto_combine_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(schedule_frame, text="Auto-combine", variable=self.auto_combine_var).grid(
            row=0, column=3, padx=10, pady=5, sticky=tk.W)

        # Auto-preprocess option
        self.auto_preprocess_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(schedule_frame, text="Auto-preprocess", variable=self.auto_preprocess_var).grid(
            row=0, column=4, padx=10, pady=5, sticky=tk.W)

        # Auto-backup option
        self.auto_backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(schedule_frame, text="Auto-backup", variable=self.auto_backup_var).grid(
            row=0, column=5, padx=10, pady=5, sticky=tk.W)

        # Row 2: Schedule controls
        # Start/stop schedule buttons
        self.schedule_running = False
        self.schedule_button_var = tk.StringVar(value="Start Schedule")
        schedule_button = ttk.Button(schedule_frame, textvariable=self.schedule_button_var,
                                    command=self.toggle_schedule)
        schedule_button.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky=tk.W)

        # Next run time
        self.next_run_var = tk.StringVar(value="Not scheduled")
        ttk.Label(schedule_frame, text="Next run:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        ttk.Label(schedule_frame, textvariable=self.next_run_var).grid(row=1, column=3, columnspan=3, padx=5, pady=5, sticky=tk.W)

        # Create a frame for action buttons
        action_frame = ttk.Frame(collection_frame)
        action_frame.pack(fill=tk.X, pady=10)

        # Collect data button
        collect_button = ttk.Button(action_frame, text="Collect Data", command=self.collect_mt5_data)
        collect_button.pack(side=tk.LEFT, padx=5)

        # Verify data button
        verify_button = ttk.Button(action_frame, text="Verify Data", command=self.verify_mt5_data)
        verify_button.pack(side=tk.LEFT, padx=5)

        # Combine data button
        combine_button = ttk.Button(action_frame, text="Combine Data", command=self.combine_mt5_data)
        combine_button.pack(side=tk.LEFT, padx=5)

        # Preprocess data button
        preprocess_button = ttk.Button(action_frame, text="Preprocess Data", command=self.preprocess_mt5_data)
        preprocess_button.pack(side=tk.LEFT, padx=5)

        # Backup data button
        backup_button = ttk.Button(action_frame, text="Backup Data", command=self.backup_mt5_data)
        backup_button.pack(side=tk.LEFT, padx=5)

        # Create a frame for progress and log
        log_frame = ttk.LabelFrame(collection_frame, text="Progress & Log")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(log_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # Log text
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create a frame for data summary
        summary_frame = ttk.LabelFrame(collection_frame, text="Data Summary")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Summary text
        self.summary_text = tk.Text(summary_frame, height=10, width=80)
        self.summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def collect_mt5_data(self):
        """Collect historical data from selected MT5 terminals using existing scripts."""
        # Get selected terminals
        selected_terminals = [i for i, var in self.terminal_vars.items() if var.get()]
        if not selected_terminals:
            messagebox.showerror("Error", "Please select at least one terminal")
            return

        # Get selected timeframes
        selected_timeframes = [tf for tf, var in self.timeframe_vars.items() if var.get()]
        if not selected_timeframes:
            messagebox.showerror("Error", "Please select at least one timeframe")
            return

        # Get symbol
        symbol = self.collection_symbol_var.get()
        if not symbol:
            messagebox.showerror("Error", "Please enter a symbol")
            return

        # Get date range
        try:
            start_date = pd.to_datetime(self.collection_start_date_var.get())
            end_date = pd.to_datetime(self.collection_end_date_var.get())

            # Calculate years based on date range
            years = (end_date - start_date).days / 365
            years = max(1, int(years) + 1)  # Ensure at least 1 year
        except Exception as e:
            messagebox.showerror("Error", f"Invalid date format: {str(e)}")
            return

        # Clear log
        self.log_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)

        # Log collection parameters
        self.log_text.insert(tk.END, f"Starting data collection with the following parameters:\n")
        self.log_text.insert(tk.END, f"Symbol: {symbol}\n")
        self.log_text.insert(tk.END, f"Terminals: {selected_terminals}\n")
        self.log_text.insert(tk.END, f"Timeframes: {selected_timeframes}\n")
        self.log_text.insert(tk.END, f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} (approx. {years} years)\n\n")
        self.log_text.see(tk.END)

        # Start collection in a separate thread to avoid freezing the GUI
        threading.Thread(target=self._collect_data_thread,
                        args=(symbol, selected_terminals, selected_timeframes, years),
                        daemon=True).start()

    def _collect_data_thread(self, symbol, _, timeframes, years):
        """Thread function to collect data from MT5 terminals using existing scripts.

        Args:
            symbol: Trading symbol
            _: Selected terminals (unused but kept for API compatibility)
            timeframes: Selected timeframes
            years: Number of years to collect
        """
        try:
            # Calculate total number of operations
            total_operations = len(timeframes)
            completed_operations = 0

            # Collection results
            collection_results = {}

            # Check if we should use collect_multi_timeframe_history.py or collect_m5_data.py
            if len(timeframes) > 1:
                # Use collect_multi_timeframe_history.py for multiple timeframes
                self.log_text.insert(tk.END, f"Using collect_multi_timeframe_history.py for multiple timeframes\n")
                self.log_text.see(tk.END)

                # Build command to collect data for all timeframes
                cmd = [
                    "python",
                    "collect_multi_timeframe_history.py",
                    "--symbol", symbol,
                    "--years", str(int(years))
                ]

                # If specific timeframes are selected, add them to the command
                if set(timeframes) != set(["M5", "M15", "M30", "H1", "H4"]):
                    # Only collect specific timeframes
                    for timeframe in timeframes:
                        # Update progress
                        completed_operations += 1
                        progress = (completed_operations / total_operations) * 100
                        self.progress_var.set(progress)

                        # Log current operation
                        self.log_text.insert(tk.END, f"Collecting {symbol} {timeframe} data from all terminals...\n")
                        self.log_text.see(tk.END)

                        # Execute command for this specific timeframe
                        timeframe_cmd = cmd + ["--timeframe", timeframe]

                        try:
                            # Execute the command
                            process = subprocess.Popen(
                                timeframe_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True,
                                bufsize=1,
                                universal_newlines=True
                            )

                            # Create a non-blocking function to read output
                            def read_output(process, _):
                                """Read process output without blocking.

                                Args:
                                    process: The subprocess to read from
                                    _: Unused parameter (kept for API compatibility)
                                """
                                while True:
                                    # Check if process has terminated
                                    if process.poll() is not None:
                                        break

                                    # Read a line (non-blocking)
                                    line = process.stdout.readline()
                                    if line:
                                        # Schedule GUI update in main thread
                                        self.root.after(10, lambda l=line: self._update_log(l))

                                    # Give the GUI time to update
                                    time.sleep(0.01)

                                # Process completed, read any remaining output
                                remaining_output, error_output = process.communicate()
                                if remaining_output:
                                    self.root.after(10, lambda o=remaining_output: self._update_log(o))
                                if error_output:
                                    self.root.after(10, lambda e=error_output: self._update_log(f"Error: {e}\n"))

                            # Start reading output in a separate thread
                            output_thread = threading.Thread(
                                target=read_output,
                                args=(process, timeframe),
                                daemon=True
                            )
                            output_thread.start()

                            # Wait for process to complete without blocking GUI
                            while process.poll() is None:
                                # Update GUI every 100ms
                                self.root.update()
                                time.sleep(0.1)

                            # Check if process was successful
                            if process.returncode == 0:
                                # Check if combined data file exists
                                output_file = Path(f"data/combined/{symbol}_{timeframe}_combined_data.parquet")
                                if output_file.exists():
                                    # Read the parquet file to get row count
                                    df = pd.read_parquet(output_file)
                                    row_count = len(df)

                                    collection_results[timeframe] = {
                                        'status': 'success',
                                        'rows': row_count,
                                        'file': str(output_file)
                                    }

                                    self.log_text.insert(tk.END, f"Successfully collected {row_count} rows of {symbol} {timeframe} data\n")
                                else:
                                    collection_results[timeframe] = {
                                        'status': 'error',
                                        'message': 'Output file not created'
                                    }

                                    self.log_text.insert(tk.END, f"Error: Output file not created for {symbol} {timeframe}\n")
                            else:
                                # Read error output
                                error_output = process.stderr.read()

                                collection_results[timeframe] = {
                                    'status': 'error',
                                    'message': error_output
                                }

                                self.log_text.insert(tk.END, f"Error collecting {symbol} {timeframe} data: {error_output}\n")

                        except Exception as e:
                            collection_results[timeframe] = {
                                'status': 'error',
                                'message': str(e)
                            }

                            self.log_text.insert(tk.END, f"Error collecting {symbol} {timeframe} data: {str(e)}\n")

                        self.log_text.see(tk.END)
                else:
                    # Collect all timeframes at once
                    self.log_text.insert(tk.END, f"Collecting data for all timeframes: {', '.join(timeframes)}\n")
                    self.log_text.see(tk.END)

                    try:
                        # Execute the command
                        process = subprocess.Popen(
                            cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            bufsize=1,
                            universal_newlines=True
                        )

                        # Create a non-blocking function to read output
                        def read_output(process):
                            while True:
                                # Check if process has terminated
                                if process.poll() is not None:
                                    break

                                # Read a line (non-blocking)
                                line = process.stdout.readline()
                                if line:
                                    # Schedule GUI update in main thread
                                    self.root.after(10, lambda l=line: self._update_log(l))

                                # Give the GUI time to update
                                time.sleep(0.01)

                            # Process completed, read any remaining output
                            remaining_output, error_output = process.communicate()
                            if remaining_output:
                                self.root.after(10, lambda o=remaining_output: self._update_log(o))
                            if error_output:
                                self.root.after(10, lambda e=error_output: self._update_log(f"Error: {e}\n"))

                        # Start reading output in a separate thread
                        output_thread = threading.Thread(
                            target=read_output,
                            args=(process,),
                            daemon=True
                        )
                        output_thread.start()

                        # Wait for process to complete without blocking GUI
                        while process.poll() is None:
                            # Update GUI every 100ms
                            self.root.update()
                            time.sleep(0.1)

                        # Check if process was successful
                        if process.returncode == 0:
                            # Check results for each timeframe
                            for timeframe in timeframes:
                                # Update progress
                                completed_operations += 1
                                progress = (completed_operations / total_operations) * 100
                                self.progress_var.set(progress)

                                # Check if combined data file exists
                                output_file = Path(f"data/combined/{symbol}_{timeframe}_combined_data.parquet")
                                if output_file.exists():
                                    # Read the parquet file to get row count
                                    df = pd.read_parquet(output_file)
                                    row_count = len(df)

                                    collection_results[timeframe] = {
                                        'status': 'success',
                                        'rows': row_count,
                                        'file': str(output_file)
                                    }

                                    self.log_text.insert(tk.END, f"Successfully collected {row_count} rows of {symbol} {timeframe} data\n")
                                else:
                                    collection_results[timeframe] = {
                                        'status': 'error',
                                        'message': 'Output file not created'
                                    }

                                    self.log_text.insert(tk.END, f"Error: Output file not created for {symbol} {timeframe}\n")
                        else:
                            # Read error output
                            error_output = process.stderr.read()

                            for timeframe in timeframes:
                                collection_results[timeframe] = {
                                    'status': 'error',
                                    'message': error_output
                                }

                            self.log_text.insert(tk.END, f"Error collecting data: {error_output}\n")

                    except Exception as e:
                        for timeframe in timeframes:
                            collection_results[timeframe] = {
                                'status': 'error',
                                'message': str(e)
                            }

                        self.log_text.insert(tk.END, f"Error collecting data: {str(e)}\n")

                    self.log_text.see(tk.END)
            else:
                # Use collect_m5_data.py for a single timeframe
                timeframe = timeframes[0]
                self.log_text.insert(tk.END, f"Using collect_m5_data.py for {timeframe} timeframe\n")
                self.log_text.see(tk.END)

                # Build command to collect data
                cmd = [
                    "python",
                    "collect_m5_data.py",
                    "--symbol", symbol,
                    "--timeframe", timeframe,
                    "--years", str(int(years))
                ]

                try:
                    # Execute the command
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        bufsize=1,
                        universal_newlines=True
                    )

                    # Create a non-blocking function to read output
                    def read_output(process):
                        while True:
                            # Check if process has terminated
                            if process.poll() is not None:
                                break

                            # Read a line (non-blocking)
                            line = process.stdout.readline()
                            if line:
                                # Schedule GUI update in main thread
                                self.root.after(10, lambda l=line: self._update_log(l))

                            # Give the GUI time to update
                            time.sleep(0.01)

                        # Process completed, read any remaining output
                        remaining_output, error_output = process.communicate()
                        if remaining_output:
                            self.root.after(10, lambda o=remaining_output: self._update_log(o))
                        if error_output:
                            self.root.after(10, lambda e=error_output: self._update_log(f"Error: {e}\n"))

                    # Start reading output in a separate thread
                    output_thread = threading.Thread(
                        target=read_output,
                        args=(process,),
                        daemon=True
                    )
                    output_thread.start()

                    # Wait for process to complete without blocking GUI
                    while process.poll() is None:
                        # Update GUI every 100ms
                        self.root.update()
                        time.sleep(0.1)

                    # Update progress
                    completed_operations += 1
                    progress = (completed_operations / total_operations) * 100
                    self.progress_var.set(progress)

                    # Check if process was successful
                    if process.returncode == 0:
                        # Check if combined data file exists
                        output_file = Path(f"data/combined/{symbol}_{timeframe}_combined_data.parquet")
                        if output_file.exists():
                            # Read the parquet file to get row count
                            df = pd.read_parquet(output_file)
                            row_count = len(df)

                            collection_results[timeframe] = {
                                'status': 'success',
                                'rows': row_count,
                                'file': str(output_file)
                            }

                            self.log_text.insert(tk.END, f"Successfully collected {row_count} rows of {symbol} {timeframe} data\n")
                        else:
                            collection_results[timeframe] = {
                                'status': 'error',
                                'message': 'Output file not created'
                            }

                            self.log_text.insert(tk.END, f"Error: Output file not created for {symbol} {timeframe}\n")
                    else:
                        # Read error output
                        error_output = process.stderr.read()

                        collection_results[timeframe] = {
                            'status': 'error',
                            'message': error_output
                        }

                        self.log_text.insert(tk.END, f"Error collecting {symbol} {timeframe} data: {error_output}\n")

                except Exception as e:
                    collection_results[timeframe] = {
                        'status': 'error',
                        'message': str(e)
                    }

                    self.log_text.insert(tk.END, f"Error collecting {symbol} {timeframe} data: {str(e)}\n")

                self.log_text.see(tk.END)

            # Update summary
            self._update_collection_summary_for_scripts(collection_results)

            # Set progress to 100%
            self.progress_var.set(100)

            # Log completion
            self.log_text.insert(tk.END, "\nData collection completed\n")
            self.log_text.see(tk.END)

        except Exception as e:
            self.log_text.insert(tk.END, f"Error in data collection thread: {str(e)}\n")
            self.log_text.see(tk.END)
            logger.error(f"Error in data collection thread: {str(e)}")

    def _update_collection_summary_for_scripts(self, collection_results):
        """Update the summary text with collection results from scripts."""
        self.summary_text.delete(1.0, tk.END)

        # Add header
        self.summary_text.insert(tk.END, "Data Collection Summary\n")
        self.summary_text.insert(tk.END, "======================\n\n")

        # Add summary for each timeframe
        for timeframe, result in collection_results.items():
            self.summary_text.insert(tk.END, f"Timeframe {timeframe}:\n")

            if result['status'] == 'success':
                self.summary_text.insert(tk.END, f"  Status: Success\n")
                self.summary_text.insert(tk.END, f"  Rows: {result['rows']}\n")
                self.summary_text.insert(tk.END, f"  File: {result['file']}\n")
            else:
                self.summary_text.insert(tk.END, f"  Status: Error\n")
                self.summary_text.insert(tk.END, f"  Message: {result.get('message', 'Unknown error')}\n")

            self.summary_text.insert(tk.END, "\n")

    def _update_collection_summary(self, collection_results):
        """Update the summary text with collection results."""
        self.summary_text.delete(1.0, tk.END)

        # Add header
        self.summary_text.insert(tk.END, "Data Collection Summary\n")
        self.summary_text.insert(tk.END, "======================\n\n")

        # Add summary for each terminal and timeframe
        for terminal_id, terminal_results in collection_results.items():
            self.summary_text.insert(tk.END, f"Terminal {terminal_id}:\n")

            for timeframe, result in terminal_results.items():
                if result['status'] == 'success':
                    self.summary_text.insert(tk.END, f"  {timeframe}: {result['rows']} rows\n")
                else:
                    self.summary_text.insert(tk.END, f"  {timeframe}: Error - {result.get('message', 'Unknown error')}\n")

            self.summary_text.insert(tk.END, "\n")

    def _update_log(self, text):
        """Update the log text in a thread-safe way."""
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, text)
            self.log_text.see(tk.END)

    def verify_mt5_data(self):
        """Verify the collected data from MT5 terminals."""
        # Get selected terminals
        selected_terminals = [i for i, var in self.terminal_vars.items() if var.get()]
        if not selected_terminals:
            messagebox.showerror("Error", "Please select at least one terminal")
            return

        # Get selected timeframes
        selected_timeframes = [tf for tf, var in self.timeframe_vars.items() if var.get()]
        if not selected_timeframes:
            messagebox.showerror("Error", "Please select at least one timeframe")
            return

        # Get symbol
        symbol = self.collection_symbol_var.get()
        if not symbol:
            messagebox.showerror("Error", "Please enter a symbol")
            return

        # Clear log
        self.log_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)

        # Log verification parameters
        self.log_text.insert(tk.END, f"Starting data verification with the following parameters:\n")
        self.log_text.insert(tk.END, f"Symbol: {symbol}\n")
        self.log_text.insert(tk.END, f"Terminals: {selected_terminals}\n")
        self.log_text.insert(tk.END, f"Timeframes: {selected_timeframes}\n\n")
        self.log_text.see(tk.END)

        # Start verification in a separate thread to avoid freezing the GUI
        threading.Thread(target=self._verify_data_thread,
                        args=(symbol, selected_terminals, selected_timeframes),
                        daemon=True).start()

    def _verify_data_thread(self, symbol, terminals, timeframes):
        """Thread function to verify data from MT5 terminals."""
        try:
            # Calculate total number of operations
            total_operations = len(terminals) * len(timeframes)
            completed_operations = 0

            # Verification results
            verification_results = {}

            for terminal_id in terminals:
                verification_results[terminal_id] = {}

                for timeframe in timeframes:
                    # Update progress
                    completed_operations += 1
                    progress = (completed_operations / total_operations) * 100
                    self.progress_var.set(progress)

                    # Log current operation
                    self.log_text.insert(tk.END, f"Verifying {symbol} {timeframe} data from Terminal {terminal_id}...\n")
                    self.log_text.see(tk.END)

                    # Check if file exists
                    file_path = Path(f"data/terminal_{terminal_id}/{symbol}_{timeframe}.csv")
                    if not file_path.exists():
                        verification_results[terminal_id][timeframe] = {
                            'status': 'error',
                            'message': 'File not found'
                        }

                        self.log_text.insert(tk.END, f"Error: File not found for {symbol} {timeframe} from Terminal {terminal_id}\n")
                        self.log_text.see(tk.END)
                        continue

                    try:
                        # Read the data
                        df = pd.read_csv(file_path)

                        # Basic verification checks
                        verification_result = {
                            'status': 'success',
                            'rows': len(df),
                            'file': str(file_path),
                            'checks': {}
                        }

                        # Check 1: Required columns
                        required_columns = ['time', 'open', 'high', 'low', 'close', 'volume']
                        missing_columns = [col for col in required_columns if col not in df.columns]

                        if missing_columns:
                            verification_result['checks']['required_columns'] = {
                                'status': 'error',
                                'message': f"Missing columns: {missing_columns}"
                            }
                        else:
                            verification_result['checks']['required_columns'] = {
                                'status': 'success'
                            }

                        # Check 2: No duplicate timestamps
                        if 'time' in df.columns:
                            df['time'] = pd.to_datetime(df['time'])
                            duplicates = df['time'].duplicated().sum()

                            if duplicates > 0:
                                verification_result['checks']['duplicates'] = {
                                    'status': 'error',
                                    'message': f"Found {duplicates} duplicate timestamps"
                                }
                            else:
                                verification_result['checks']['duplicates'] = {
                                    'status': 'success'
                                }

                        # Check 3: No missing values
                        missing_values = df.isnull().sum().sum()

                        if missing_values > 0:
                            verification_result['checks']['missing_values'] = {
                                'status': 'error',
                                'message': f"Found {missing_values} missing values"
                            }
                        else:
                            verification_result['checks']['missing_values'] = {
                                'status': 'success'
                            }

                        # Check 4: Date range
                        if 'time' in df.columns:
                            date_range = f"{df['time'].min()} to {df['time'].max()}"
                            verification_result['checks']['date_range'] = {
                                'status': 'info',
                                'message': date_range
                            }

                        # Check 5: Timeframe consistency
                        if 'time' in df.columns and len(df) > 1:
                            # Sort by time to ensure correct calculation
                            df = df.sort_values('time')

                            # Calculate time differences
                            time_diffs = df['time'].diff().dropna()

                            # Get the most common time difference
                            if not time_diffs.empty:
                                most_common_diff = time_diffs.mode().iloc[0]
                                expected_diff = None

                                # Expected time difference based on timeframe
                                if timeframe == 'M5':
                                    expected_diff = pd.Timedelta(minutes=5)
                                elif timeframe == 'M15':
                                    expected_diff = pd.Timedelta(minutes=15)
                                elif timeframe == 'M30':
                                    expected_diff = pd.Timedelta(minutes=30)
                                elif timeframe == 'H1':
                                    expected_diff = pd.Timedelta(hours=1)
                                elif timeframe == 'H4':
                                    expected_diff = pd.Timedelta(hours=4)

                                if expected_diff and most_common_diff != expected_diff:
                                    verification_result['checks']['timeframe_consistency'] = {
                                        'status': 'error',
                                        'message': f"Expected {expected_diff}, found {most_common_diff}"
                                    }
                                else:
                                    verification_result['checks']['timeframe_consistency'] = {
                                        'status': 'success'
                                    }

                        # Advanced verification checks

                        # Check 6: Price gaps (significant jumps in price)
                        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
                            # Calculate percentage changes in close price
                            pct_changes = df['close'].pct_change().abs()

                            # Define threshold for significant price gaps (e.g., 5%)
                            threshold = 0.05

                            # Find significant gaps
                            significant_gaps = pct_changes[pct_changes > threshold]

                            if len(significant_gaps) > 0:
                                # Get the top 5 largest gaps
                                top_gaps = significant_gaps.nlargest(5)
                                gap_info = []

                                for idx, gap in top_gaps.items():
                                    time_idx = df.index[df.index.get_loc(idx)]
                                    gap_time = df.loc[time_idx, 'time']
                                    gap_info.append(f"{gap_time}: {gap:.2%}")

                                verification_result['checks']['price_gaps'] = {
                                    'status': 'warning',
                                    'message': f"Found {len(significant_gaps)} significant price gaps (>{threshold:.0%}). Top 5: {', '.join(gap_info)}"
                                }
                            else:
                                verification_result['checks']['price_gaps'] = {
                                    'status': 'success'
                                }

                        # Check 7: OHLC consistency (high >= open, high >= close, low <= open, low <= close)
                        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
                            # Check high >= open
                            high_open_inconsistent = (df['high'] < df['open']).sum()

                            # Check high >= close
                            high_close_inconsistent = (df['high'] < df['close']).sum()

                            # Check low <= open
                            low_open_inconsistent = (df['low'] > df['open']).sum()

                            # Check low <= close
                            low_close_inconsistent = (df['low'] > df['close']).sum()

                            total_inconsistencies = high_open_inconsistent + high_close_inconsistent + low_open_inconsistent + low_close_inconsistent

                            if total_inconsistencies > 0:
                                verification_result['checks']['ohlc_consistency'] = {
                                    'status': 'error',
                                    'message': f"Found {total_inconsistencies} OHLC inconsistencies: high<open: {high_open_inconsistent}, high<close: {high_close_inconsistent}, low>open: {low_open_inconsistent}, low>close: {low_close_inconsistent}"
                                }
                            else:
                                verification_result['checks']['ohlc_consistency'] = {
                                    'status': 'success'
                                }

                        # Check 8: Volume anomalies (unusually high or zero volumes)
                        if 'volume' in df.columns:
                            # Check for zero volumes
                            zero_volumes = (df['volume'] == 0).sum()

                            # Check for unusually high volumes (e.g., > 3 standard deviations from mean)
                            mean_volume = df['volume'].mean()
                            std_volume = df['volume'].std()
                            threshold = mean_volume + 3 * std_volume
                            high_volumes = (df['volume'] > threshold).sum()

                            if zero_volumes > 0 or high_volumes > 0:
                                verification_result['checks']['volume_anomalies'] = {
                                    'status': 'warning',
                                    'message': f"Found {zero_volumes} zero volumes and {high_volumes} unusually high volumes (>{threshold:.0f})"
                                }
                            else:
                                verification_result['checks']['volume_anomalies'] = {
                                    'status': 'success'
                                }

                        # Check 9: Missing time periods (gaps in time series)
                        if 'time' in df.columns and len(df) > 1:
                            # Get expected time delta
                            expected_diff = None
                            if timeframe == 'M5':
                                expected_diff = pd.Timedelta(minutes=5)
                            elif timeframe == 'M15':
                                expected_diff = pd.Timedelta(minutes=15)
                            elif timeframe == 'M30':
                                expected_diff = pd.Timedelta(minutes=30)
                            elif timeframe == 'H1':
                                expected_diff = pd.Timedelta(hours=1)
                            elif timeframe == 'H4':
                                expected_diff = pd.Timedelta(hours=4)

                            if expected_diff:
                                # Calculate time differences
                                time_diffs = df['time'].diff()

                                # Find gaps (time differences > expected)
                                gaps = time_diffs[time_diffs > expected_diff * 1.5]

                                if len(gaps) > 0:
                                    # Get the top 5 largest gaps
                                    top_gaps = gaps.nlargest(5)
                                    gap_info = []

                                    for idx, gap in top_gaps.items():
                                        time_idx = df.index[df.index.get_loc(idx)]
                                        gap_time = df.loc[time_idx, 'time']
                                        gap_info.append(f"{gap_time}: {gap}")

                                    verification_result['checks']['time_gaps'] = {
                                        'status': 'warning',
                                        'message': f"Found {len(gaps)} gaps in time series. Top 5: {', '.join(gap_info)}"
                                    }
                                else:
                                    verification_result['checks']['time_gaps'] = {
                                        'status': 'success'
                                    }

                        # Check 10: Data quality score
                        # Calculate an overall data quality score based on all checks
                        total_checks = 0
                        passed_checks = 0

                        for check_name, check_result in verification_result['checks'].items():
                            if check_result['status'] != 'info':  # Skip info checks
                                total_checks += 1
                                if check_result['status'] == 'success':
                                    passed_checks += 1

                        if total_checks > 0:
                            quality_score = (passed_checks / total_checks) * 100
                            verification_result['checks']['quality_score'] = {
                                'status': 'info',
                                'message': f"Data quality score: {quality_score:.1f}%"
                            }

                        # Store verification result
                        verification_results[terminal_id][timeframe] = verification_result

                        # Log verification result
                        self.log_text.insert(tk.END, f"Verified {len(df)} rows of {symbol} {timeframe} data from Terminal {terminal_id}\n")

                        # Log any issues
                        for check_name, check_result in verification_result['checks'].items():
                            if check_result['status'] == 'error':
                                self.log_text.insert(tk.END, f"  - {check_name}: {check_result['message']}\n")

                        self.log_text.see(tk.END)

                    except Exception as e:
                        verification_results[terminal_id][timeframe] = {
                            'status': 'error',
                            'message': str(e)
                        }

                        self.log_text.insert(tk.END, f"Error verifying {symbol} {timeframe} data from Terminal {terminal_id}: {str(e)}\n")
                        self.log_text.see(tk.END)

            # Update summary
            self._update_verification_summary(verification_results)

            # Set progress to 100%
            self.progress_var.set(100)

            # Log completion
            self.log_text.insert(tk.END, "\nData verification completed\n")
            self.log_text.see(tk.END)

        except Exception as e:
            self.log_text.insert(tk.END, f"Error in data verification thread: {str(e)}\n")
            self.log_text.see(tk.END)
            logger.error(f"Error in data verification thread: {str(e)}")

    def _update_verification_summary(self, verification_results):
        """Update the summary text with verification results."""
        self.summary_text.delete(1.0, tk.END)

        # Add header
        self.summary_text.insert(tk.END, "Data Verification Summary\n")
        self.summary_text.insert(tk.END, "=======================\n\n")

        # Add summary for each terminal and timeframe
        for terminal_id, terminal_results in verification_results.items():
            self.summary_text.insert(tk.END, f"Terminal {terminal_id}:\n")

            for timeframe, result in terminal_results.items():
                if result['status'] == 'success':
                    self.summary_text.insert(tk.END, f"  {timeframe}: {result['rows']} rows - ")

                    # Count errors
                    error_count = sum(1 for check in result.get('checks', {}).values()
                                    if check.get('status') == 'error')

                    if error_count == 0:
                        self.summary_text.insert(tk.END, "All checks passed\n")
                    else:
                        self.summary_text.insert(tk.END, f"{error_count} issues found\n")
                else:
                    self.summary_text.insert(tk.END, f"  {timeframe}: Error - {result.get('message', 'Unknown error')}\n")

            self.summary_text.insert(tk.END, "\n")

    def combine_mt5_data(self):
        """Combine data from multiple MT5 terminals."""
        # Get selected terminals
        selected_terminals = [i for i, var in self.terminal_vars.items() if var.get()]
        if not selected_terminals:
            messagebox.showerror("Error", "Please select at least one terminal")
            return

        # Get selected timeframes
        selected_timeframes = [tf for tf, var in self.timeframe_vars.items() if var.get()]
        if not selected_timeframes:
            messagebox.showerror("Error", "Please select at least one timeframe")
            return

        # Get symbol
        symbol = self.collection_symbol_var.get()
        if not symbol:
            messagebox.showerror("Error", "Please enter a symbol")
            return

        # Clear log
        self.log_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)

        # Log combination parameters
        self.log_text.insert(tk.END, f"Starting data combination with the following parameters:\n")
        self.log_text.insert(tk.END, f"Symbol: {symbol}\n")
        self.log_text.insert(tk.END, f"Terminals: {selected_terminals}\n")
        self.log_text.insert(tk.END, f"Timeframes: {selected_timeframes}\n\n")
        self.log_text.see(tk.END)

        # Start combination in a separate thread to avoid freezing the GUI
        threading.Thread(target=self._combine_data_thread,
                        args=(symbol, selected_terminals, selected_timeframes),
                        daemon=True).start()

    def _combine_data_thread(self, symbol, terminals, timeframes):
        """Thread function to combine data from MT5 terminals."""
        try:
            # Calculate total number of operations
            total_operations = len(timeframes)
            completed_operations = 0

            # Create combined data directory if it doesn't exist
            combined_dir = Path("data/combined")
            combined_dir.mkdir(parents=True, exist_ok=True)

            # Combination results
            combination_results = {}

            for timeframe in timeframes:
                # Update progress
                completed_operations += 1
                progress = (completed_operations / total_operations) * 100
                self.progress_var.set(progress)

                # Log current operation
                self.log_text.insert(tk.END, f"Combining {symbol} {timeframe} data from terminals {terminals}...\n")
                self.log_text.see(tk.END)

                # Initialize result for this timeframe
                combination_results[timeframe] = {
                    'status': 'pending',
                    'terminals_processed': 0,
                    'total_rows': 0,
                    'unique_rows': 0,
                    'output_file': f"data/combined/{symbol}_{timeframe}_combined_data.csv"
                }

                # Collect data from all terminals
                all_data = []
                terminals_processed = 0

                for terminal_id in terminals:
                    file_path = Path(f"data/terminal_{terminal_id}/{symbol}_{timeframe}.csv")

                    if file_path.exists():
                        try:
                            # Read data
                            df = pd.read_csv(file_path)

                            # Ensure time column is present
                            if 'time' not in df.columns:
                                self.log_text.insert(tk.END, f"Warning: 'time' column not found in {file_path}, skipping\n")
                                self.log_text.see(tk.END)
                                continue

                            # Convert time to datetime
                            df['time'] = pd.to_datetime(df['time'])

                            # Add terminal_id column
                            df['terminal_id'] = terminal_id

                            # Add to list
                            all_data.append(df)
                            terminals_processed += 1

                            self.log_text.insert(tk.END, f"Added {len(df)} rows from Terminal {terminal_id}\n")
                            self.log_text.see(tk.END)

                        except Exception as e:
                            self.log_text.insert(tk.END, f"Error reading {file_path}: {str(e)}\n")
                            self.log_text.see(tk.END)
                    else:
                        self.log_text.insert(tk.END, f"File not found: {file_path}\n")
                        self.log_text.see(tk.END)

                # Update terminals processed
                combination_results[timeframe]['terminals_processed'] = terminals_processed

                # Check if we have data to combine
                if not all_data:
                    combination_results[timeframe]['status'] = 'error'
                    combination_results[timeframe]['message'] = 'No data to combine'

                    self.log_text.insert(tk.END, f"Error: No data to combine for {symbol} {timeframe}\n")
                    self.log_text.see(tk.END)
                    continue

                # Combine data
                combined_df = pd.concat(all_data, ignore_index=True)

                # Update total rows
                combination_results[timeframe]['total_rows'] = len(combined_df)

                # Remove duplicates based on time
                combined_df = combined_df.drop_duplicates(subset=['time'])

                # Update unique rows
                combination_results[timeframe]['unique_rows'] = len(combined_df)

                # Sort by time
                combined_df = combined_df.sort_values('time')

                # Save combined data
                output_file = Path(combination_results[timeframe]['output_file'])
                combined_df.to_csv(output_file, index=False)

                # Also save as parquet for faster loading
                parquet_file = output_file.with_suffix('.parquet')
                combined_df.to_parquet(parquet_file, index=False)

                # Update status
                combination_results[timeframe]['status'] = 'success'

                # Log success
                self.log_text.insert(tk.END, f"Successfully combined data for {symbol} {timeframe}\n")
                self.log_text.insert(tk.END, f"Total rows: {combination_results[timeframe]['total_rows']}\n")
                self.log_text.insert(tk.END, f"Unique rows: {combination_results[timeframe]['unique_rows']}\n")
                self.log_text.insert(tk.END, f"Output files: {output_file} and {parquet_file}\n\n")
                self.log_text.see(tk.END)

            # Update summary
            self._update_combination_summary(combination_results)

            # Set progress to 100%
            self.progress_var.set(100)

            # Log completion
            self.log_text.insert(tk.END, "\nData combination completed\n")
            self.log_text.see(tk.END)

        except Exception as e:
            self.log_text.insert(tk.END, f"Error in data combination thread: {str(e)}\n")
            self.log_text.see(tk.END)
            logger.error(f"Error in data combination thread: {str(e)}")

    def _update_combination_summary(self, combination_results):
        """Update the summary text with combination results."""
        self.summary_text.delete(1.0, tk.END)

        # Add header
        self.summary_text.insert(tk.END, "Data Combination Summary\n")
        self.summary_text.insert(tk.END, "=======================\n\n")

        # Add summary for each timeframe
        for timeframe, result in combination_results.items():
            self.summary_text.insert(tk.END, f"{timeframe}:\n")

            if result['status'] == 'success':
                self.summary_text.insert(tk.END, f"  Status: Success\n")
                self.summary_text.insert(tk.END, f"  Terminals processed: {result['terminals_processed']}\n")
                self.summary_text.insert(tk.END, f"  Total rows: {result['total_rows']}\n")
                self.summary_text.insert(tk.END, f"  Unique rows: {result['unique_rows']}\n")
                self.summary_text.insert(tk.END, f"  Output file: {result['output_file']}\n")
            else:
                self.summary_text.insert(tk.END, f"  Status: Error\n")
                self.summary_text.insert(tk.END, f"  Message: {result.get('message', 'Unknown error')}\n")

            self.summary_text.insert(tk.END, "\n")

    def preprocess_mt5_data(self):
        """Preprocess the collected data with technical indicators and other options."""
        # Get selected terminals
        selected_terminals = [i for i, var in self.terminal_vars.items() if var.get()]
        if not selected_terminals:
            messagebox.showerror("Error", "Please select at least one terminal")
            return

        # Get selected timeframes
        selected_timeframes = [tf for tf, var in self.timeframe_vars.items() if var.get()]
        if not selected_timeframes:
            messagebox.showerror("Error", "Please select at least one timeframe")
            return

        # Get symbol
        symbol = self.collection_symbol_var.get()
        if not symbol:
            messagebox.showerror("Error", "Please enter a symbol")
            return

        # Get preprocessing options
        selected_indicators = [name for name, var in self.preprocess_vars.items() if var.get()]
        fill_missing = self.fill_missing_var.get()
        remove_outliers = self.remove_outliers_var.get()
        normalize_data = self.normalize_var.get()
        add_custom_features = self.custom_features_var.get()

        # Clear log
        self.log_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)

        # Log preprocessing parameters
        self.log_text.insert(tk.END, f"Starting data preprocessing with the following parameters:\n")
        self.log_text.insert(tk.END, f"Symbol: {symbol}\n")
        self.log_text.insert(tk.END, f"Terminals: {selected_terminals}\n")
        self.log_text.insert(tk.END, f"Timeframes: {selected_timeframes}\n")
        self.log_text.insert(tk.END, f"Technical Indicators: {selected_indicators}\n")
        self.log_text.insert(tk.END, f"Fill Missing Values: {fill_missing}\n")
        self.log_text.insert(tk.END, f"Remove Outliers: {remove_outliers}\n")
        self.log_text.insert(tk.END, f"Normalize Data: {normalize_data}\n")
        self.log_text.insert(tk.END, f"Add Custom Features: {add_custom_features}\n\n")
        self.log_text.see(tk.END)

        # Start preprocessing in a separate thread to avoid freezing the GUI
        threading.Thread(target=self._preprocess_data_thread,
                        args=(symbol, selected_terminals, selected_timeframes,
                              selected_indicators, fill_missing, remove_outliers,
                              normalize_data, add_custom_features),
                        daemon=True).start()

    def _preprocess_data_thread(self, symbol, terminals, timeframes, indicators,
                               fill_missing, remove_outliers, normalize_data, add_custom_features):
        """Thread function to preprocess data."""
        try:
            # Calculate total number of operations
            total_operations = len(terminals) * len(timeframes)
            completed_operations = 0

            # Create preprocessed data directory if it doesn't exist
            preprocessed_dir = Path("data/preprocessed")
            preprocessed_dir.mkdir(parents=True, exist_ok=True)

            # Preprocessing results
            preprocessing_results = {}

            for terminal_id in terminals:
                preprocessing_results[terminal_id] = {}

                for timeframe in timeframes:
                    # Update progress
                    completed_operations += 1
                    progress = (completed_operations / total_operations) * 100
                    self.progress_var.set(progress)

                    # Log current operation
                    self.log_text.insert(tk.END, f"Preprocessing {symbol} {timeframe} data from Terminal {terminal_id}...\n")
                    self.log_text.see(tk.END)

                    # Initialize result for this timeframe
                    preprocessing_results[terminal_id][timeframe] = {
                        'status': 'pending',
                        'indicators_added': [],
                        'rows_before': 0,
                        'rows_after': 0,
                        'output_file': f"data/preprocessed/terminal_{terminal_id}_{symbol}_{timeframe}_preprocessed.csv"
                    }

                    # Check if input file exists
                    input_file = Path(f"data/terminal_{terminal_id}/{symbol}_{timeframe}.csv")
                    if not input_file.exists():
                        preprocessing_results[terminal_id][timeframe]['status'] = 'error'
                        preprocessing_results[terminal_id][timeframe]['message'] = 'Input file not found'

                        self.log_text.insert(tk.END, f"Error: Input file not found for {symbol} {timeframe} from Terminal {terminal_id}\n")
                        self.log_text.see(tk.END)
                        continue

                    try:
                        # Read data
                        df = pd.read_csv(input_file)

                        # Store original row count
                        preprocessing_results[terminal_id][timeframe]['rows_before'] = len(df)

                        # Ensure time column is present and convert to datetime
                        if 'time' not in df.columns:
                            preprocessing_results[terminal_id][timeframe]['status'] = 'error'
                            preprocessing_results[terminal_id][timeframe]['message'] = "'time' column not found"

                            self.log_text.insert(tk.END, f"Error: 'time' column not found in {input_file}\n")
                            self.log_text.see(tk.END)
                            continue

                        df['time'] = pd.to_datetime(df['time'])

                        # Sort by time
                        df = df.sort_values('time')

                        # Fill missing values if requested
                        if fill_missing:
                            # Check for missing values
                            missing_before = df.isnull().sum().sum()

                            if missing_before > 0:
                                # Fill missing values with forward fill, then backward fill
                                df = df.ffill().bfill()

                                missing_after = df.isnull().sum().sum()
                                self.log_text.insert(tk.END, f"Filled {missing_before - missing_after} missing values\n")
                                self.log_text.see(tk.END)

                        # Remove outliers if requested
                        if remove_outliers:
                            # Define columns to check for outliers
                            price_cols = ['open', 'high', 'low', 'close']

                            # Count rows before
                            rows_before = len(df)

                            # Remove outliers using z-score method
                            for col in price_cols:
                                if col in df.columns:
                                    # Calculate z-score
                                    z_scores = (df[col] - df[col].mean()) / df[col].std()

                                    # Define threshold (e.g., 3 standard deviations)
                                    threshold = 3

                                    # Create mask for non-outliers
                                    mask = (z_scores.abs() <= threshold)

                                    # Apply mask
                                    df = df[mask]

                            # Count rows after
                            rows_after = len(df)
                            outliers_removed = rows_before - rows_after

                            if outliers_removed > 0:
                                self.log_text.insert(tk.END, f"Removed {outliers_removed} outliers\n")
                                self.log_text.see(tk.END)

                        # Add technical indicators if requested
                        indicators_added = []

                        if 'rsi' in indicators:
                            # Add Relative Strength Index
                            if 'close' in df.columns:
                                window = 14  # Default RSI window
                                delta = df['close'].diff()
                                gain = delta.where(delta > 0, 0)
                                loss = -delta.where(delta < 0, 0)

                                avg_gain = gain.rolling(window=window).mean()
                                avg_loss = loss.rolling(window=window).mean()

                                rs = avg_gain / avg_loss
                                df['rsi'] = 100 - (100 / (1 + rs))

                                indicators_added.append('rsi')
                                self.log_text.insert(tk.END, f"Added RSI indicator\n")
                                self.log_text.see(tk.END)

                        if 'macd' in indicators:
                            # Add Moving Average Convergence Divergence
                            if 'close' in df.columns:
                                # Calculate MACD line
                                ema12 = df['close'].ewm(span=12, adjust=False).mean()
                                ema26 = df['close'].ewm(span=26, adjust=False).mean()
                                df['macd'] = ema12 - ema26

                                # Calculate signal line
                                df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()

                                # Calculate histogram
                                df['macd_hist'] = df['macd'] - df['macd_signal']

                                indicators_added.extend(['macd', 'macd_signal', 'macd_hist'])
                                self.log_text.insert(tk.END, f"Added MACD indicators\n")
                                self.log_text.see(tk.END)

                        if 'bb' in indicators:
                            # Add Bollinger Bands
                            if 'close' in df.columns:
                                window = 20  # Default Bollinger Bands window
                                std_dev = 2  # Default standard deviation

                                # Calculate middle band (SMA)
                                df['bb_middle'] = df['close'].rolling(window=window).mean()

                                # Calculate standard deviation
                                rolling_std = df['close'].rolling(window=window).std()

                                # Calculate upper and lower bands
                                df['bb_upper'] = df['bb_middle'] + (rolling_std * std_dev)
                                df['bb_lower'] = df['bb_middle'] - (rolling_std * std_dev)

                                indicators_added.extend(['bb_middle', 'bb_upper', 'bb_lower'])
                                self.log_text.insert(tk.END, f"Added Bollinger Bands indicators\n")
                                self.log_text.see(tk.END)

                        if 'atr' in indicators:
                            # Add Average True Range
                            if all(col in df.columns for col in ['high', 'low', 'close']):
                                window = 14  # Default ATR window

                                # Calculate True Range
                                df['tr1'] = df['high'] - df['low']
                                df['tr2'] = abs(df['high'] - df['close'].shift())
                                df['tr3'] = abs(df['low'] - df['close'].shift())
                                df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

                                # Calculate ATR
                                df['atr'] = df['tr'].rolling(window=window).mean()

                                # Drop temporary columns
                                df = df.drop(['tr1', 'tr2', 'tr3', 'tr'], axis=1)

                                indicators_added.append('atr')
                                self.log_text.insert(tk.END, f"Added ATR indicator\n")
                                self.log_text.see(tk.END)

                        if 'ema' in indicators:
                            # Add Exponential Moving Averages
                            if 'close' in df.columns:
                                # Add different EMA periods
                                for period in [9, 21, 50, 200]:
                                    df[f'ema_{period}'] = df['close'].ewm(span=period, adjust=False).mean()
                                    indicators_added.append(f'ema_{period}')

                                self.log_text.insert(tk.END, f"Added EMA indicators\n")
                                self.log_text.see(tk.END)

                        # Add custom features if requested
                        if add_custom_features:
                            # Add price change features
                            if 'close' in df.columns:
                                # Add percentage change
                                df['pct_change'] = df['close'].pct_change()

                                # Add log returns
                                df['log_return'] = np.log(df['close'] / df['close'].shift(1))

                                # Add rolling volatility
                                df['volatility_21'] = df['log_return'].rolling(window=21).std()

                                indicators_added.extend(['pct_change', 'log_return', 'volatility_21'])
                                self.log_text.insert(tk.END, f"Added custom price change features\n")
                                self.log_text.see(tk.END)

                            # Add candle pattern features
                            if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
                                # Add candle body size
                                df['body_size'] = abs(df['close'] - df['open'])

                                # Add upper shadow
                                df['upper_shadow'] = df.apply(
                                    lambda x: x['high'] - max(x['open'], x['close']), axis=1)

                                # Add lower shadow
                                df['lower_shadow'] = df.apply(
                                    lambda x: min(x['open'], x['close']) - x['low'], axis=1)

                                # Add candle type (bullish or bearish)
                                df['bullish'] = (df['close'] > df['open']).astype(int)

                                indicators_added.extend(['body_size', 'upper_shadow', 'lower_shadow', 'bullish'])
                                self.log_text.insert(tk.END, f"Added candle pattern features\n")
                                self.log_text.see(tk.END)

                        # Normalize data if requested
                        if normalize_data:
                            # Define columns to normalize (exclude time and categorical features)
                            exclude_cols = ['time', 'bullish']
                            numeric_cols = [col for col in df.columns if col not in exclude_cols]

                            # Apply min-max normalization
                            for col in numeric_cols:
                                min_val = df[col].min()
                                max_val = df[col].max()

                                if max_val > min_val:  # Avoid division by zero
                                    df[f'{col}_norm'] = (df[col] - min_val) / (max_val - min_val)
                                    indicators_added.append(f'{col}_norm')

                            self.log_text.insert(tk.END, f"Normalized numeric features\n")
                            self.log_text.see(tk.END)

                        # Drop rows with NaN values (typically at the beginning due to indicators)
                        rows_before_drop = len(df)
                        df = df.dropna()
                        rows_after_drop = len(df)

                        if rows_before_drop > rows_after_drop:
                            self.log_text.insert(tk.END, f"Dropped {rows_before_drop - rows_after_drop} rows with NaN values\n")
                            self.log_text.see(tk.END)

                        # Store final row count
                        preprocessing_results[terminal_id][timeframe]['rows_after'] = len(df)

                        # Store indicators added
                        preprocessing_results[terminal_id][timeframe]['indicators_added'] = indicators_added

                        # Save preprocessed data
                        output_file = Path(preprocessing_results[terminal_id][timeframe]['output_file'])
                        df.to_csv(output_file, index=False)

                        # Also save as parquet for faster loading
                        parquet_file = output_file.with_suffix('.parquet')
                        df.to_parquet(parquet_file, index=False)

                        # Update status
                        preprocessing_results[terminal_id][timeframe]['status'] = 'success'

                        # Log success
                        self.log_text.insert(tk.END, f"Successfully preprocessed {symbol} {timeframe} data from Terminal {terminal_id}\n")
                        self.log_text.insert(tk.END, f"Added {len(indicators_added)} indicators/features\n")
                        self.log_text.insert(tk.END, f"Output files: {output_file} and {parquet_file}\n\n")
                        self.log_text.see(tk.END)

                    except Exception as e:
                        preprocessing_results[terminal_id][timeframe]['status'] = 'error'
                        preprocessing_results[terminal_id][timeframe]['message'] = str(e)

                        self.log_text.insert(tk.END, f"Error preprocessing {symbol} {timeframe} data from Terminal {terminal_id}: {str(e)}\n")
                        self.log_text.see(tk.END)

            # Update summary
            self._update_preprocessing_summary(preprocessing_results)

            # Set progress to 100%
            self.progress_var.set(100)

            # Log completion
            self.log_text.insert(tk.END, "\nData preprocessing completed\n")
            self.log_text.see(tk.END)

        except Exception as e:
            self.log_text.insert(tk.END, f"Error in data preprocessing thread: {str(e)}\n")
            self.log_text.see(tk.END)
            logger.error(f"Error in data preprocessing thread: {str(e)}")

    def _update_preprocessing_summary(self, preprocessing_results):
        """Update the summary text with preprocessing results."""
        self.summary_text.delete(1.0, tk.END)

        # Add header
        self.summary_text.insert(tk.END, "Data Preprocessing Summary\n")
        self.summary_text.insert(tk.END, "=========================\n\n")

        # Add summary for each terminal and timeframe
        for terminal_id, terminal_results in preprocessing_results.items():
            self.summary_text.insert(tk.END, f"Terminal {terminal_id}:\n")

            for timeframe, result in terminal_results.items():
                if result['status'] == 'success':
                    self.summary_text.insert(tk.END, f"  {timeframe}: Success\n")
                    self.summary_text.insert(tk.END, f"    - Rows before: {result['rows_before']}\n")
                    self.summary_text.insert(tk.END, f"    - Rows after: {result['rows_after']}\n")
                    self.summary_text.insert(tk.END, f"    - Indicators added: {len(result['indicators_added'])}\n")

                    # List the indicators added (up to 10)
                    if result['indicators_added']:
                        indicators_to_show = result['indicators_added'][:10]
                        if len(result['indicators_added']) > 10:
                            indicators_to_show.append("...")
                        self.summary_text.insert(tk.END, f"    - Indicators: {', '.join(indicators_to_show)}\n")

                    self.summary_text.insert(tk.END, f"    - Output file: {result['output_file']}\n")
                else:
                    self.summary_text.insert(tk.END, f"  {timeframe}: Error\n")
                    self.summary_text.insert(tk.END, f"    - Message: {result.get('message', 'Unknown error')}\n")

            self.summary_text.insert(tk.END, "\n")

    def toggle_schedule(self):
        """Toggle scheduled data collection on/off."""
        if self.schedule_running:
            # Stop the schedule
            self.schedule_running = False
            self.schedule_button_var.set("Start Schedule")
            self.next_run_var.set("Not scheduled")

            # Log
            self.log_text.insert(tk.END, "Scheduled data collection stopped\n")
            self.log_text.see(tk.END)
            logger.info("Scheduled data collection stopped")
        else:
            # Start the schedule
            self.schedule_running = True
            self.schedule_button_var.set("Stop Schedule")

            # Calculate next run time
            next_run_time = self._calculate_next_run_time()
            self.next_run_var.set(next_run_time.strftime("%Y-%m-%d %H:%M:%S"))

            # Log
            self.log_text.insert(tk.END, f"Scheduled data collection started. Next run: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            self.log_text.see(tk.END)
            logger.info(f"Scheduled data collection started. Next run: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Start the scheduler in a separate thread
            threading.Thread(target=self._scheduler_thread, daemon=True).start()

    def _calculate_next_run_time(self):
        """Calculate the next run time based on the selected interval."""
        interval = self.schedule_interval_var.get()
        now = datetime.now()

        # Parse the interval
        if interval.endswith('m'):
            minutes = int(interval[:-1])
            next_run = now + timedelta(minutes=minutes)
        elif interval.endswith('h'):
            hours = int(interval[:-1])
            next_run = now + timedelta(hours=hours)
        elif interval.endswith('d'):
            days = int(interval[:-1])
            next_run = now + timedelta(days=days)
        else:
            # Default to 1 hour
            next_run = now + timedelta(hours=1)

        return next_run

    def _scheduler_thread(self):
        """Thread function for scheduled data collection."""
        while self.schedule_running:
            # Get the next run time
            next_run_time = self._calculate_next_run_time()

            # Update the next run time display
            self.next_run_var.set(next_run_time.strftime("%Y-%m-%d %H:%M:%S"))

            # Calculate seconds until next run
            now = datetime.now()
            seconds_to_wait = (next_run_time - now).total_seconds()

            # Wait until next run time
            for _ in range(int(seconds_to_wait)):
                if not self.schedule_running:
                    return
                time.sleep(1)

            # Run the scheduled tasks
            if self.schedule_running:
                self._run_scheduled_tasks()

    def _run_scheduled_tasks(self):
        """Run the scheduled data collection tasks."""
        try:
            # Get parameters
            symbol = self.collection_symbol_var.get()
            selected_terminals = [i for i, var in self.terminal_vars.items() if var.get()]
            selected_timeframes = [tf for tf, var in self.timeframe_vars.items() if var.get()]

            # Log
            self.log_text.insert(tk.END, f"\n=== Scheduled Run: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            self.log_text.see(tk.END)

            # Collect data
            self.log_text.insert(tk.END, "Running scheduled data collection...\n")
            self.log_text.see(tk.END)

            # Get date range (last 2 days by default)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=2)

            # Run data collection
            self._collect_data_thread(symbol, selected_terminals, selected_timeframes,
                                     start_date, end_date)

            # Auto-combine if enabled
            if self.auto_combine_var.get():
                self.log_text.insert(tk.END, "Running auto-combine...\n")
                self.log_text.see(tk.END)
                self._combine_data_thread(symbol, selected_terminals, selected_timeframes)

            # Auto-preprocess if enabled
            if self.auto_preprocess_var.get():
                self.log_text.insert(tk.END, "Running auto-preprocess...\n")
                self.log_text.see(tk.END)

                # Get preprocessing options
                selected_indicators = [name for name, var in self.preprocess_vars.items() if var.get()]
                fill_missing = self.fill_missing_var.get()
                remove_outliers = self.remove_outliers_var.get()
                normalize_data = self.normalize_var.get()
                add_custom_features = self.custom_features_var.get()

                self._preprocess_data_thread(symbol, selected_terminals, selected_timeframes,
                                           selected_indicators, fill_missing, remove_outliers,
                                           normalize_data, add_custom_features)

            # Auto-backup if enabled
            if self.auto_backup_var.get():
                self.log_text.insert(tk.END, "Running auto-backup...\n")
                self.log_text.see(tk.END)
                self._backup_data_thread(symbol, selected_terminals, selected_timeframes)

            # Log completion
            self.log_text.insert(tk.END, f"Scheduled run completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            self.log_text.see(tk.END)

            # Calculate next run time
            next_run_time = self._calculate_next_run_time()
            self.next_run_var.set(next_run_time.strftime("%Y-%m-%d %H:%M:%S"))

        except Exception as e:
            error_msg = f"Error in scheduled tasks: {str(e)}"
            self.log_text.insert(tk.END, f"{error_msg}\n")
            self.log_text.see(tk.END)
            logger.error(error_msg)

    def backup_mt5_data(self):
        """Backup collected data to prevent data loss."""
        # Get selected terminals
        selected_terminals = [i for i, var in self.terminal_vars.items() if var.get()]
        if not selected_terminals:
            messagebox.showerror("Error", "Please select at least one terminal")
            return

        # Get selected timeframes
        selected_timeframes = [tf for tf, var in self.timeframe_vars.items() if var.get()]
        if not selected_timeframes:
            messagebox.showerror("Error", "Please select at least one timeframe")
            return

        # Get symbol
        symbol = self.collection_symbol_var.get()
        if not symbol:
            messagebox.showerror("Error", "Please enter a symbol")
            return

        # Clear log
        self.log_text.delete(1.0, tk.END)
        self.summary_text.delete(1.0, tk.END)

        # Log backup parameters
        self.log_text.insert(tk.END, f"Starting data backup with the following parameters:\n")
        self.log_text.insert(tk.END, f"Symbol: {symbol}\n")
        self.log_text.insert(tk.END, f"Terminals: {selected_terminals}\n")
        self.log_text.insert(tk.END, f"Timeframes: {selected_timeframes}\n\n")
        self.log_text.see(tk.END)

        # Start backup in a separate thread to avoid freezing the GUI
        threading.Thread(target=self._backup_data_thread,
                        args=(symbol, selected_terminals, selected_timeframes),
                        daemon=True).start()

    def _backup_data_thread(self, symbol, terminals, timeframes):
        """Thread function to backup data."""
        try:
            # Calculate total number of operations
            total_operations = len(terminals) * len(timeframes) + 2  # +2 for combined and preprocessed data
            completed_operations = 0

            # Create backup directory with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = Path(f"data/backup/{timestamp}")
            backup_dir.mkdir(parents=True, exist_ok=True)

            # Backup results
            backup_results = {
                'timestamp': timestamp,
                'backup_dir': str(backup_dir),
                'terminals': {},
                'combined': {'status': 'pending', 'files_backed_up': 0},
                'preprocessed': {'status': 'pending', 'files_backed_up': 0}
            }

            # Backup terminal data
            for terminal_id in terminals:
                backup_results['terminals'][terminal_id] = {
                    'status': 'pending',
                    'files_backed_up': 0
                }

                # Create terminal backup directory
                terminal_backup_dir = backup_dir / f"terminal_{terminal_id}"
                terminal_backup_dir.mkdir(exist_ok=True)

                files_backed_up = 0

                for timeframe in timeframes:
                    # Update progress
                    completed_operations += 1
                    progress = (completed_operations / total_operations) * 100
                    self.progress_var.set(progress)

                    # Log current operation
                    self.log_text.insert(tk.END, f"Backing up {symbol} {timeframe} data from Terminal {terminal_id}...\n")
                    self.log_text.see(tk.END)

                    # Source file
                    source_file = Path(f"data/terminal_{terminal_id}/{symbol}_{timeframe}.csv")

                    if source_file.exists():
                        # Destination file
                        dest_file = terminal_backup_dir / f"{symbol}_{timeframe}.csv"

                        # Copy file
                        import shutil
                        shutil.copy2(source_file, dest_file)

                        files_backed_up += 1
                        self.log_text.insert(tk.END, f"Backed up {source_file} to {dest_file}\n")
                        self.log_text.see(tk.END)
                    else:
                        self.log_text.insert(tk.END, f"File not found: {source_file}\n")
                        self.log_text.see(tk.END)

                # Update backup results
                backup_results['terminals'][terminal_id]['status'] = 'success'
                backup_results['terminals'][terminal_id]['files_backed_up'] = files_backed_up

            # Backup combined data
            combined_backup_dir = backup_dir / "combined"
            combined_backup_dir.mkdir(exist_ok=True)

            combined_files_backed_up = 0

            for timeframe in timeframes:
                # Source files (both CSV and Parquet)
                source_files = [
                    Path(f"data/combined/{symbol}_{timeframe}_combined_data.csv"),
                    Path(f"data/combined/{symbol}_{timeframe}_combined_data.parquet")
                ]

                for source_file in source_files:
                    if source_file.exists():
                        # Destination file
                        dest_file = combined_backup_dir / source_file.name

                        # Copy file
                        import shutil
                        shutil.copy2(source_file, dest_file)

                        combined_files_backed_up += 1
                        self.log_text.insert(tk.END, f"Backed up {source_file} to {dest_file}\n")
                        self.log_text.see(tk.END)

            # Update progress
            completed_operations += 1
            progress = (completed_operations / total_operations) * 100
            self.progress_var.set(progress)

            # Update backup results
            backup_results['combined']['status'] = 'success'
            backup_results['combined']['files_backed_up'] = combined_files_backed_up

            # Backup preprocessed data
            preprocessed_backup_dir = backup_dir / "preprocessed"
            preprocessed_backup_dir.mkdir(exist_ok=True)

            preprocessed_files_backed_up = 0

            for terminal_id in terminals:
                for timeframe in timeframes:
                    # Source files (both CSV and Parquet)
                    source_files = [
                        Path(f"data/preprocessed/terminal_{terminal_id}_{symbol}_{timeframe}_preprocessed.csv"),
                        Path(f"data/preprocessed/terminal_{terminal_id}_{symbol}_{timeframe}_preprocessed.parquet")
                    ]

                    for source_file in source_files:
                        if source_file.exists():
                            # Destination file
                            dest_file = preprocessed_backup_dir / source_file.name

                            # Copy file
                            import shutil
                            shutil.copy2(source_file, dest_file)

                            preprocessed_files_backed_up += 1
                            self.log_text.insert(tk.END, f"Backed up {source_file} to {dest_file}\n")
                            self.log_text.see(tk.END)

            # Update progress
            completed_operations += 1
            progress = (completed_operations / total_operations) * 100
            self.progress_var.set(progress)

            # Update backup results
            backup_results['preprocessed']['status'] = 'success'
            backup_results['preprocessed']['files_backed_up'] = preprocessed_files_backed_up

            # Create a backup info file
            import json
            with open(backup_dir / "backup_info.json", "w") as f:
                json.dump({
                    'timestamp': timestamp,
                    'symbol': symbol,
                    'terminals': terminals,
                    'timeframes': timeframes,
                    'files_backed_up': {
                        'terminals': {tid: res['files_backed_up'] for tid, res in backup_results['terminals'].items()},
                        'combined': backup_results['combined']['files_backed_up'],
                        'preprocessed': backup_results['preprocessed']['files_backed_up']
                    }
                }, f, indent=4)

            # Update summary
            self._update_backup_summary(backup_results)

            # Set progress to 100%
            self.progress_var.set(100)

            # Log completion
            self.log_text.insert(tk.END, f"\nData backup completed. Backup directory: {backup_dir}\n")
            self.log_text.see(tk.END)

        except Exception as e:
            self.log_text.insert(tk.END, f"Error in data backup thread: {str(e)}\n")
            self.log_text.see(tk.END)
            logger.error(f"Error in data backup thread: {str(e)}")

    def _update_backup_summary(self, backup_results):
        """Update the summary text with backup results."""
        self.summary_text.delete(1.0, tk.END)

        # Add header
        self.summary_text.insert(tk.END, "Data Backup Summary\n")
        self.summary_text.insert(tk.END, "=================\n\n")

        # Add backup info
        self.summary_text.insert(tk.END, f"Backup Timestamp: {backup_results['timestamp']}\n")
        self.summary_text.insert(tk.END, f"Backup Directory: {backup_results['backup_dir']}\n\n")

        # Add terminal backup summary
        self.summary_text.insert(tk.END, "Terminal Data:\n")
        for terminal_id, result in backup_results['terminals'].items():
            self.summary_text.insert(tk.END, f"  Terminal {terminal_id}: {result['files_backed_up']} files\n")

        # Add combined data backup summary
        self.summary_text.insert(tk.END, "\nCombined Data:\n")
        self.summary_text.insert(tk.END, f"  {backup_results['combined']['files_backed_up']} files\n")

        # Add preprocessed data backup summary
        self.summary_text.insert(tk.END, "\nPreprocessed Data:\n")
        self.summary_text.insert(tk.END, f"  {backup_results['preprocessed']['files_backed_up']} files\n")

        # Add total
        total_files = (
            sum(result['files_backed_up'] for result in backup_results['terminals'].values()) +
            backup_results['combined']['files_backed_up'] +
            backup_results['preprocessed']['files_backed_up']
        )
        self.summary_text.insert(tk.END, f"\nTotal Files Backed Up: {total_files}\n")

    def create_data_tab(self):
        """Create the Data Management tab."""
        data_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(data_frame, text="Data Management")

        # Add a label
        ttk.Label(data_frame, text="Data Management", font=("Arial", 16)).pack(pady=10)

        # Create a frame for data loading
        load_frame = ttk.LabelFrame(data_frame, text="Load Data")
        load_frame.pack(fill=tk.X, pady=5)

        # Symbol selection
        ttk.Label(load_frame, text="Symbol:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.symbol_var = tk.StringVar(value="BTCUSD.a")
        symbol_entry = ttk.Entry(load_frame, textvariable=self.symbol_var, width=15)
        symbol_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # Timeframe selection
        ttk.Label(load_frame, text="Timeframe:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.timeframe_var = tk.StringVar(value="M5")
        timeframe_combo = ttk.Combobox(load_frame, textvariable=self.timeframe_var, values=["M5", "M15", "M30", "H1", "H4"])
        timeframe_combo.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)

        # Load button
        load_button = ttk.Button(load_frame, text="Load Data", command=self.load_data)
        load_button.grid(row=0, column=4, padx=5, pady=5)

        # Data info frame
        info_frame = ttk.LabelFrame(data_frame, text="Data Information")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Data info text
        self.data_info_text = tk.Text(info_frame, height=10, width=80)
        self.data_info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Preprocessing frame
        preprocess_frame = ttk.LabelFrame(data_frame, text="Preprocessing")
        preprocess_frame.pack(fill=tk.X, pady=5)

        # Preprocessing options
        ttk.Label(preprocess_frame, text="Sequence Length:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.seq_length_var = tk.StringVar(value="60")
        seq_length_entry = ttk.Entry(preprocess_frame, textvariable=self.seq_length_var, width=10)
        seq_length_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # Preprocess button
        preprocess_button = ttk.Button(preprocess_frame, text="Preprocess Data", command=self.preprocess_data)
        preprocess_button.grid(row=0, column=2, padx=5, pady=5)

    def create_model_tab(self):
        """Create the Model Management tab."""
        model_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(model_frame, text="Model Management")

        # Add a label
        ttk.Label(model_frame, text="Model Management", font=("Arial", 16)).pack(pady=10)

        # Create a frame for model selection
        select_frame = ttk.LabelFrame(model_frame, text="Model Selection")
        select_frame.pack(fill=tk.X, pady=5)

        # Model type selection
        ttk.Label(select_frame, text="Model Type:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.model_type_var = tk.StringVar(value="TFT")
        model_type_combo = ttk.Combobox(select_frame, textvariable=self.model_type_var,
                                        values=["TFT", "LSTM", "ARIMA"])
        model_type_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # Model configuration frame
        config_frame = ttk.LabelFrame(model_frame, text="Model Configuration")
        config_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Configuration text
        self.config_text = tk.Text(config_frame, height=10, width=80)
        self.config_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Training frame
        train_frame = ttk.LabelFrame(model_frame, text="Training")
        train_frame.pack(fill=tk.X, pady=5)

        # Training options
        ttk.Label(train_frame, text="Epochs:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.epochs_var = tk.StringVar(value="100")
        epochs_entry = ttk.Entry(train_frame, textvariable=self.epochs_var, width=10)
        epochs_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(train_frame, text="Batch Size:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.batch_size_var = tk.StringVar(value="32")
        batch_size_entry = ttk.Entry(train_frame, textvariable=self.batch_size_var, width=10)
        batch_size_entry.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)

        # Train button
        train_button = ttk.Button(train_frame, text="Train Model", command=self.train_model)
        train_button.grid(row=0, column=4, padx=5, pady=5)

        # Load model button
        load_model_button = ttk.Button(train_frame, text="Load Model", command=self.load_model)
        load_model_button.grid(row=0, column=5, padx=5, pady=5)

    def create_trading_tab(self):
        """Create the Trading tab."""
        trading_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(trading_frame, text="Trading")

        # Add a label
        ttk.Label(trading_frame, text="Trading", font=("Arial", 16)).pack(pady=10)

        # Create a frame for backtest settings
        backtest_frame = ttk.LabelFrame(trading_frame, text="Backtest Settings")
        backtest_frame.pack(fill=tk.X, pady=5)

        # Backtest options
        ttk.Label(backtest_frame, text="Start Date:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.start_date_var = tk.StringVar(value="2023-01-01")
        start_date_entry = ttk.Entry(backtest_frame, textvariable=self.start_date_var, width=15)
        start_date_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(backtest_frame, text="End Date:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.end_date_var = tk.StringVar(value="2023-12-31")
        end_date_entry = ttk.Entry(backtest_frame, textvariable=self.end_date_var, width=15)
        end_date_entry.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)

        # Backtest button
        backtest_button = ttk.Button(backtest_frame, text="Run Backtest", command=self.run_backtest)
        backtest_button.grid(row=0, column=4, padx=5, pady=5)

        # Results frame
        results_frame = ttk.LabelFrame(trading_frame, text="Backtest Results")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Results text
        self.results_text = tk.Text(results_frame, height=10, width=80)
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_visualization_tab(self):
        """Create the Visualization tab."""
        viz_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(viz_frame, text="Visualization")

        # Add a label
        ttk.Label(viz_frame, text="Visualization", font=("Arial", 16)).pack(pady=10)

        # Create a frame for chart settings
        chart_frame = ttk.LabelFrame(viz_frame, text="Chart Settings")
        chart_frame.pack(fill=tk.X, pady=5)

        # Chart options
        ttk.Label(chart_frame, text="Chart Type:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.chart_type_var = tk.StringVar(value="Price & Predictions")
        chart_type_combo = ttk.Combobox(chart_frame, textvariable=self.chart_type_var,
                                       values=["Price & Predictions", "Performance Metrics", "Attention Weights"])
        chart_type_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        # Update chart button
        update_button = ttk.Button(chart_frame, text="Update Chart", command=self.update_chart)
        update_button.grid(row=0, column=2, padx=5, pady=5)

        # Chart frame
        self.chart_display_frame = ttk.LabelFrame(viz_frame, text="Chart")
        self.chart_display_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Placeholder for chart
        ttk.Label(self.chart_display_frame, text="Chart will be displayed here").pack(pady=100)

    def load_data(self):
        """Load data based on selected symbol and timeframe."""
        symbol = self.symbol_var.get()
        timeframe = self.timeframe_var.get()

        self.status_var.set(f"Loading {symbol} {timeframe} data...")

        try:
            # Try to load from the standard location
            data_file = Path(f"data/combined/{symbol}_{timeframe}.parquet")

            # If file doesn't exist, try the CSV version
            if not data_file.exists():
                data_file = Path(f"data/combined/{symbol}_{timeframe}_combined_data.csv")

            # If still doesn't exist, ask user to select a file
            if not data_file.exists():
                messagebox.showinfo("File Not Found",
                                   f"Could not find data file for {symbol} {timeframe}. Please select a file.")
                data_file = filedialog.askopenfilename(
                    title=f"Select {symbol} {timeframe} data file",
                    filetypes=[("Parquet files", "*.parquet"), ("CSV files", "*.csv"), ("All files", "*.*")]
                )

                if not data_file:  # User cancelled
                    self.status_var.set("Data loading cancelled")
                    return

                data_file = Path(data_file)

            # Load the data
            if data_file.suffix.lower() == '.parquet':
                df = pd.read_parquet(data_file)
            elif data_file.suffix.lower() == '.csv':
                df = pd.read_csv(data_file)
            else:
                raise ValueError(f"Unsupported file format: {data_file.suffix}")

            # Convert 'time' column to datetime if it exists and is not the index
            if 'time' in df.columns and not isinstance(df.index, pd.DatetimeIndex):
                df['time'] = pd.to_datetime(df['time'])
                df = df.set_index('time')

            # Store the dataframe
            self.df = df

            # Display data info
            self.data_info_text.delete(1.0, tk.END)
            self.data_info_text.insert(tk.END, f"Loaded {symbol} {timeframe} data from {data_file}\n\n")
            self.data_info_text.insert(tk.END, f"Data shape: {df.shape}\n")
            self.data_info_text.insert(tk.END, f"Date range: {df.index.min()} to {df.index.max()}\n\n")
            self.data_info_text.insert(tk.END, "Columns:\n")
            for col in df.columns:
                self.data_info_text.insert(tk.END, f"- {col}\n")

            self.data_info_text.insert(tk.END, "\nSample data:\n")
            self.data_info_text.insert(tk.END, df.head().to_string())

            # Create a simple price chart
            self.create_price_chart(df)

            self.status_var.set(f"Loaded {symbol} {timeframe} data with {len(df)} rows")
            logger.info(f"Loaded {symbol} {timeframe} data from {data_file} with {len(df)} rows")

        except Exception as e:
            error_msg = f"Error loading data: {str(e)}"
            self.status_var.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("Data Loading Error", error_msg)

    def create_price_chart(self, df):
        """Create a simple price chart in the data tab."""
        # Create a new frame for the chart if it doesn't exist
        if not hasattr(self, 'data_chart_frame'):
            self.data_chart_frame = ttk.LabelFrame(self.notebook.nametowidget(self.notebook.tabs()[0]), text="Price Chart")
            self.data_chart_frame.pack(fill=tk.BOTH, expand=True, pady=5, after=self.data_info_text)

        # Clear the frame
        for widget in self.data_chart_frame.winfo_children():
            widget.destroy()

        # Create a figure and axis
        fig, ax = plt.subplots(figsize=(10, 5))

        # Plot close prices
        if 'close' in df.columns:
            ax.plot(df.index[-500:], df['close'][-500:], label='Close Price')
            ax.set_title('Close Price (Last 500 periods)')
            ax.set_xlabel('Date')
            ax.set_ylabel('Price')
            ax.legend()
            ax.grid(True)

        # Create a canvas to display the figure
        canvas = FigureCanvasTkAgg(fig, master=self.data_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def preprocess_data(self):
        """Preprocess the loaded data."""
        if not hasattr(self, 'df') or self.df is None:
            messagebox.showerror("Error", "Please load data first")
            return

        try:
            seq_length = int(self.seq_length_var.get())

            self.status_var.set("Preprocessing data...")

            # Create a copy of the dataframe to avoid modifying the original
            df = self.df.copy()

            # Basic preprocessing steps

            # 1. Handle missing values
            df = df.dropna()

            # 2. Select relevant features
            feature_cols = ['open', 'high', 'low', 'close']
            if 'volume' in df.columns:
                feature_cols.append('volume')

            # Add technical indicators if they exist
            for col in ['rsi', 'macd', 'bb_upper', 'bb_lower', 'atr']:
                if col in df.columns:
                    feature_cols.append(col)

            # 3. Create sequences for model input
            X = []
            y = []

            # Target is next period's close price change
            df['target'] = df['close'].pct_change(1).shift(-1)

            # Drop rows with NaN values
            df = df.dropna()

            # Create sequences
            for i in range(len(df) - seq_length):
                X.append(df[feature_cols].iloc[i:i+seq_length].values)
                y.append(df['target'].iloc[i+seq_length])

            X = np.array(X)
            y = np.array(y)

            # Split into train and validation sets (80/20)
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]

            # Store the preprocessed data
            self.X_train = X_train
            self.X_val = X_val
            self.y_train = y_train
            self.y_val = y_val
            self.feature_cols = feature_cols

            # Display preprocessing info
            self.data_info_text.insert(tk.END, f"\n\nPreprocessed data with sequence length {seq_length}\n")
            self.data_info_text.insert(tk.END, f"Features: {feature_cols}\n")
            self.data_info_text.insert(tk.END, f"Training data shape: X={X_train.shape}, y={y_train.shape}\n")
            self.data_info_text.insert(tk.END, f"Validation data shape: X={X_val.shape}, y={y_val.shape}\n")

            # Create a visualization of the sequences
            self.visualize_sequences(X_train, y_train, feature_cols)

            self.status_var.set("Data preprocessing complete")
            logger.info(f"Preprocessed data with sequence length {seq_length}, "
                       f"X_train shape: {X_train.shape}, y_train shape: {y_train.shape}")

        except Exception as e:
            error_msg = f"Error preprocessing data: {str(e)}"
            self.status_var.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("Preprocessing Error", error_msg)

    def visualize_sequences(self, X_train, y_train, feature_cols, num_sequences=3):
        """Visualize a few sequences from the training data."""
        # Create a new frame for the visualization if it doesn't exist
        if not hasattr(self, 'sequence_viz_frame'):
            self.sequence_viz_frame = ttk.LabelFrame(
                self.notebook.nametowidget(self.notebook.tabs()[0]),
                text="Sequence Visualization"
            )
            self.sequence_viz_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Clear the frame
        for widget in self.sequence_viz_frame.winfo_children():
            widget.destroy()

        # Create a figure with subplots for each feature
        fig, axes = plt.subplots(len(feature_cols), 1, figsize=(10, 2*len(feature_cols)), sharex=True)

        # If there's only one feature, axes will not be an array
        if len(feature_cols) == 1:
            axes = [axes]

        # Get a few random sequences
        indices = np.random.choice(len(X_train), min(num_sequences, len(X_train)), replace=False)

        # Plot each feature for each sequence
        for i, idx in enumerate(indices):
            sequence = X_train[idx]
            # target value is not used in the plot but kept for reference
            # target = y_train[idx]

            for j, feature in enumerate(feature_cols):
                axes[j].plot(sequence[:, j], label=f"Seq {i+1}")
                axes[j].set_ylabel(feature)
                axes[j].grid(True)

        # Set common labels and title
        axes[-1].set_xlabel("Time Step")
        fig.suptitle(f"Sample Sequences (Target values: {', '.join([f'{y_train[idx]:.4f}' for idx in indices])})")

        # Add legend to the first subplot
        axes[0].legend()

        # Adjust layout
        plt.tight_layout()

        # Create a canvas to display the figure
        canvas = FigureCanvasTkAgg(fig, master=self.sequence_viz_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def train_model(self):
        """Train the selected model."""
        # Check if data is preprocessed
        if not hasattr(self, 'X_train') or self.X_train is None:
            messagebox.showerror("Error", "Please load and preprocess data first")
            return

        model_type = self.model_type_var.get()

        try:
            epochs = int(self.epochs_var.get())
            batch_size = int(self.batch_size_var.get())

            self.status_var.set(f"Training {model_type} model...")

            # Get symbol and timeframe
            symbol = self.symbol_var.get()
            timeframe = self.timeframe_var.get()

            # Create model configuration
            model_config = {
                'model_name': model_type.lower(),
                'timeframe': timeframe,
                'terminal_id': '1',
                'symbol': symbol,
                'input_dim': self.X_train.shape[2],
                'output_dim': 1,
                'sequence_length': self.X_train.shape[1],
                'hidden_size': 64,
                'attention_head_size': 4 if model_type == 'TFT' else None,
                'dropout_rate': 0.1,
                'num_lstm_layers': 2 if model_type in ['TFT', 'LSTM'] else None,
                'learning_rate': 0.001,
                'batch_size': batch_size,
                'epochs': epochs,
                'patience': 10,
                'models_base_path': 'models',
                'FEATURE_COLUMNS': self.feature_cols
            }

            # Display model configuration
            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(tk.END, f"Training {model_type} model\n\n")
            self.config_text.insert(tk.END, "Model Configuration:\n")
            for key, value in model_config.items():
                if value is not None:
                    self.config_text.insert(tk.END, f"- {key}: {value}\n")

            # Initialize and train the model based on type
            if model_type == 'TFT':
                # Import the TFT model
                from models.tft_model import TFTModel

                # Initialize model
                model = TFTModel(model_config)

                # Build model
                model.build()

                # Train model
                # The history is stored in the model object itself
                model.train(self.X_train, self.y_train, validation_data=(self.X_val, self.y_val))

                # Store the model
                self.model = model

            elif model_type == 'LSTM':
                # Import the LSTM model
                from models.pytorch_lstm_model import LSTMModel

                # Initialize model
                model = LSTMModel(model_config)

                # Build model
                model.build()

                # Train model
                # The history is stored in the model object itself
                model.train(self.X_train, self.y_train, validation_data=(self.X_val, self.y_val))

                # Store the model
                self.model = model

            elif model_type == 'ARIMA':
                # Import the ARIMA model
                from utils.arima_trainer import ARIMAModel

                # Initialize model
                model = ARIMAModel(model_config)

                # Prepare data for ARIMA (use only the close prices)
                close_idx = self.feature_cols.index('close') if 'close' in self.feature_cols else 0
                train_data = self.X_train[:, -1, close_idx]  # Use the last timestep of each sequence
                val_data = self.X_val[:, -1, close_idx]

                # Train model
                model.train(train_data, self.y_train, validation_data=(val_data, self.y_val))

                # Store the model
                self.model = model

            else:
                raise ValueError(f"Unsupported model type: {model_type}")

            # Display training results
            self.config_text.insert(tk.END, "\nTraining Results:\n")

            if hasattr(model, 'history') and model.history:
                # Display final loss and metrics
                if 'loss' in model.history:
                    self.config_text.insert(tk.END, f"- Final loss: {model.history['loss'][-1]:.6f}\n")
                if 'val_loss' in model.history:
                    self.config_text.insert(tk.END, f"- Final validation loss: {model.history['val_loss'][-1]:.6f}\n")
                if 'mae' in model.history:
                    self.config_text.insert(tk.END, f"- Final MAE: {model.history['mae'][-1]:.6f}\n")
                if 'val_mae' in model.history:
                    self.config_text.insert(tk.END, f"- Final validation MAE: {model.history['val_mae'][-1]:.6f}\n")

            # Evaluate model on validation data
            if hasattr(model, 'evaluate'):
                metrics = model.evaluate(self.X_val, self.y_val, self.feature_cols)
                self.config_text.insert(tk.END, "\nValidation Metrics:\n")
                for key, value in metrics.items():
                    self.config_text.insert(tk.END, f"- {key}: {value:.6f}\n")

            # Visualize training history
            if hasattr(model, 'history') and model.history:
                self.visualize_training_history(model.history)

            # Save model
            if hasattr(model, 'save'):
                model.save()
                self.config_text.insert(tk.END, f"\nModel saved to {model.model_path}\n")

            self.status_var.set(f"{model_type} model training complete")
            logger.info(f"Trained {model_type} model with {epochs} epochs and batch size {batch_size}")

        except Exception as e:
            error_msg = f"Error training model: {str(e)}"
            self.status_var.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("Training Error", error_msg)

    def visualize_training_history(self, history):
        """Visualize the training history."""
        # Create a new frame for the visualization if it doesn't exist
        if not hasattr(self, 'history_viz_frame'):
            self.history_viz_frame = ttk.LabelFrame(
                self.notebook.nametowidget(self.notebook.tabs()[1]),
                text="Training History"
            )
            self.history_viz_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Clear the frame
        for widget in self.history_viz_frame.winfo_children():
            widget.destroy()

        # Create a figure with subplots
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))

        # Plot loss
        axes[0].plot(history['loss'], label='Training Loss')
        if 'val_loss' in history:
            axes[0].plot(history['val_loss'], label='Validation Loss')
        axes[0].set_title('Loss')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True)

        # Plot metrics (MAE if available)
        if 'mae' in history:
            axes[1].plot(history['mae'], label='Training MAE')
            if 'val_mae' in history:
                axes[1].plot(history['val_mae'], label='Validation MAE')
            axes[1].set_title('Mean Absolute Error')
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('MAE')
            axes[1].legend()
            axes[1].grid(True)

        # Adjust layout
        plt.tight_layout()

        # Create a canvas to display the figure
        canvas = FigureCanvasTkAgg(fig, master=self.history_viz_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def load_model(self):
        """Load a pre-trained model."""
        model_type = self.model_type_var.get()

        try:
            # Get symbol and timeframe
            symbol = self.symbol_var.get()
            timeframe = self.timeframe_var.get()

            self.status_var.set(f"Loading {model_type} model...")

            # Ask user to select model directory
            model_dir = filedialog.askdirectory(
                title=f"Select {model_type} model directory",
                initialdir=f"models/1/{timeframe}/{model_type.lower()}_model"
            )

            if not model_dir:  # User cancelled
                self.status_var.set("Model loading cancelled")
                return

            # Create model configuration
            model_config = {
                'model_name': model_type.lower(),
                'timeframe': timeframe,
                'terminal_id': '1',
                'symbol': symbol,
                'models_base_path': 'models',
                'model_path': model_dir
            }

            # Load the model based on type
            if model_type == 'TFT':
                # Import the TFT model
                from models.tft_model import TFTModel

                # Initialize model
                model = TFTModel(model_config)

                # CRITICAL FIX: Use load() method which has fallback to build() when files are missing
                if hasattr(model, 'load'):
                    model.load()
                else:
                    logger.warning(f"Model has no load() method")

                # Store the model
                self.model = model

            elif model_type == 'LSTM':
                # Import the LSTM model
                from models.pytorch_lstm_model import LSTMModel

                # Initialize model
                model = LSTMModel(model_config)

                # CRITICAL FIX: Use load() method which has fallback to build() when files are missing
                if hasattr(model, 'load'):
                    model.load()
                else:
                    logger.warning(f"Model has no load() method")

                # Store the model
                self.model = model

            elif model_type == 'ARIMA':
                # Import the ARIMA model
                from utils.arima_trainer import ARIMAModel

                # Initialize model
                model = ARIMAModel(model_config)

                # CRITICAL FIX: Use load() method which has fallback to build() when files are missing
                if hasattr(model, 'load'):
                    model.load()
                else:
                    logger.warning(f"Model has no load() method")

                # Store the model
                self.model = model

            else:
                raise ValueError(f"Unsupported model type: {model_type}")

            # Display model information
            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(tk.END, f"Loaded {model_type} model from {model_dir}\n\n")

            # Display model configuration if available
            if hasattr(model, 'config'):
                self.config_text.insert(tk.END, "Model Configuration:\n")
                for key, value in model.config.items():
                    if value is not None:
                        self.config_text.insert(tk.END, f"- {key}: {value}\n")

            # Check if we have preprocessed data
            if hasattr(self, 'X_val') and self.X_val is not None:
                # Evaluate model on validation data
                if hasattr(model, 'evaluate'):
                    try:
                        metrics = model.evaluate(self.X_val, self.y_val, self.feature_cols)
                        self.config_text.insert(tk.END, "\nValidation Metrics:\n")
                        for key, value in metrics.items():
                            self.config_text.insert(tk.END, f"- {key}: {value:.6f}\n")
                    except Exception as e:
                        self.config_text.insert(tk.END, f"\nError evaluating model: {str(e)}\n")

            self.status_var.set(f"{model_type} model loaded from {model_dir}")
            logger.info(f"Loaded {model_type} model from {model_dir}")

        except Exception as e:
            error_msg = f"Error loading model: {str(e)}"
            self.status_var.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("Model Loading Error", error_msg)

    def run_backtest(self):
        """Run a backtest with the selected model."""
        # Check if model is loaded
        if not hasattr(self, 'model') or self.model is None:
            messagebox.showerror("Error", "Please train or load a model first")
            return

        # Check if data is loaded
        if not hasattr(self, 'df') or self.df is None:
            messagebox.showerror("Error", "Please load data first")
            return

        try:
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            # Convert to datetime
            start_date = pd.to_datetime(start_date)
            end_date = pd.to_datetime(end_date)

            self.status_var.set("Running backtest...")

            # Filter data for backtest period
            backtest_df = self.df.copy()
            if isinstance(backtest_df.index, pd.DatetimeIndex):
                backtest_df = backtest_df.loc[start_date:end_date]
            else:
                # If index is not datetime, try to filter using a 'time' column
                if 'time' in backtest_df.columns:
                    backtest_df['time'] = pd.to_datetime(backtest_df['time'])
                    backtest_df = backtest_df[(backtest_df['time'] >= start_date) & (backtest_df['time'] <= end_date)]

            if len(backtest_df) == 0:
                raise ValueError(f"No data found for period {start_date} to {end_date}")

            # Get sequence length from model config
            seq_length = self.model.config.get('sequence_length', 60)

            # Prepare features
            feature_cols = self.feature_cols if hasattr(self, 'feature_cols') else ['open', 'high', 'low', 'close', 'volume']

            # Create target variable (next period's return)
            backtest_df['target'] = backtest_df['close'].pct_change(1).shift(-1)

            # Drop rows with NaN values
            backtest_df = backtest_df.dropna()

            # Create sequences for model input
            X = []
            actual_returns = []
            dates = []

            for i in range(len(backtest_df) - seq_length):
                X.append(backtest_df[feature_cols].iloc[i:i+seq_length].values)
                actual_returns.append(backtest_df['target'].iloc[i+seq_length])
                dates.append(backtest_df.index[i+seq_length])

            X = np.array(X)
            actual_returns = np.array(actual_returns)

            # Make predictions
            model_type = self.model_type_var.get()

            if model_type == 'ARIMA':
                # For ARIMA, use the last value of each sequence
                close_idx = feature_cols.index('close') if 'close' in feature_cols else 0
                last_values = X[:, -1, close_idx]
                predicted_returns = self.model.predict(last_values).flatten()
            else:
                predicted_returns = self.model.predict(X).flatten()

            # Create a DataFrame with results
            results_df = pd.DataFrame({
                'date': dates,
                'actual_return': actual_returns,
                'predicted_return': predicted_returns
            })

            # Calculate trading signals (1 for buy, -1 for sell, 0 for hold)
            results_df['signal'] = np.sign(results_df['predicted_return'])

            # Calculate strategy returns
            results_df['strategy_return'] = results_df['signal'].shift(1) * results_df['actual_return']

            # Calculate cumulative returns
            results_df['cumulative_actual'] = (1 + results_df['actual_return']).cumprod() - 1
            results_df['cumulative_strategy'] = (1 + results_df['strategy_return']).cumprod() - 1

            # Calculate performance metrics
            total_trades = (results_df['signal'].diff() != 0).sum()
            winning_trades = (results_df['strategy_return'] > 0).sum()
            losing_trades = (results_df['strategy_return'] < 0).sum()
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Calculate profit factor
            gross_profit = results_df.loc[results_df['strategy_return'] > 0, 'strategy_return'].sum()
            gross_loss = abs(results_df.loc[results_df['strategy_return'] < 0, 'strategy_return'].sum())
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

            # Calculate Sharpe ratio
            strategy_return_mean = results_df['strategy_return'].mean()
            strategy_return_std = results_df['strategy_return'].std()
            sharpe_ratio = strategy_return_mean / strategy_return_std * np.sqrt(252) if strategy_return_std > 0 else 0

            # Calculate maximum drawdown
            cumulative = (1 + results_df['strategy_return']).cumprod()
            max_dd = (cumulative / cumulative.cummax() - 1).min()

            # Display results
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"Backtest Results\n")
            self.results_text.insert(tk.END, f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}\n")
            self.results_text.insert(tk.END, f"Model: {model_type}\n\n")

            self.results_text.insert(tk.END, f"Performance Metrics:\n")
            self.results_text.insert(tk.END, f"- Total Trades: {total_trades}\n")
            self.results_text.insert(tk.END, f"- Winning Trades: {winning_trades}\n")
            self.results_text.insert(tk.END, f"- Losing Trades: {losing_trades}\n")
            self.results_text.insert(tk.END, f"- Win Rate: {win_rate:.2%}\n")
            self.results_text.insert(tk.END, f"- Profit Factor: {profit_factor:.2f}\n")
            self.results_text.insert(tk.END, f"- Sharpe Ratio: {sharpe_ratio:.2f}\n")
            self.results_text.insert(tk.END, f"- Maximum Drawdown: {max_dd:.2%}\n")
            self.results_text.insert(tk.END, f"- Final Return: {results_df['cumulative_strategy'].iloc[-1]:.2%}\n")

            # Visualize backtest results
            self.visualize_backtest_results(results_df)

            self.status_var.set("Backtest complete")
            logger.info(f"Ran backtest from {start_date} to {end_date} with {model_type} model")

        except Exception as e:
            error_msg = f"Error running backtest: {str(e)}"
            self.status_var.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("Backtest Error", error_msg)

    def visualize_backtest_results(self, results_df):
        """Visualize backtest results."""
        # Create a new frame for the visualization if it doesn't exist
        if not hasattr(self, 'backtest_viz_frame'):
            self.backtest_viz_frame = ttk.LabelFrame(
                self.notebook.nametowidget(self.notebook.tabs()[2]),
                text="Backtest Visualization"
            )
            self.backtest_viz_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Clear the frame
        for widget in self.backtest_viz_frame.winfo_children():
            widget.destroy()

        # Create a figure with subplots
        fig, axes = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

        # Plot cumulative returns
        axes[0].plot(results_df['date'], results_df['cumulative_actual'], label='Buy & Hold')
        axes[0].plot(results_df['date'], results_df['cumulative_strategy'], label='Strategy')
        axes[0].set_title('Cumulative Returns')
        axes[0].set_ylabel('Return')
        axes[0].legend()
        axes[0].grid(True)

        # Plot signals and returns
        axes[1].plot(results_df['date'], results_df['actual_return'], label='Actual Return', alpha=0.5)
        axes[1].plot(results_df['date'], results_df['predicted_return'], label='Predicted Return', alpha=0.5)

        # Plot buy/sell signals
        buy_signals = results_df[results_df['signal'] == 1]
        sell_signals = results_df[results_df['signal'] == -1]

        axes[1].scatter(buy_signals['date'], buy_signals['actual_return'],
                       color='green', marker='^', label='Buy Signal')
        axes[1].scatter(sell_signals['date'], sell_signals['actual_return'],
                       color='red', marker='v', label='Sell Signal')

        axes[1].set_title('Returns and Signals')
        axes[1].set_xlabel('Date')
        axes[1].set_ylabel('Return')
        axes[1].legend()
        axes[1].grid(True)

        # Adjust layout
        plt.tight_layout()

        # Create a canvas to display the figure
        canvas = FigureCanvasTkAgg(fig, master=self.backtest_viz_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def update_chart(self):
        """Update the chart based on selected chart type."""
        chart_type = self.chart_type_var.get()

        # Check if model is loaded
        if not hasattr(self, 'model') or self.model is None:
            messagebox.showerror("Error", "Please train or load a model first")
            return

        self.status_var.set(f"Updating {chart_type} chart...")

        try:
            # Clear the chart frame
            for widget in self.chart_display_frame.winfo_children():
                widget.destroy()

            if chart_type == "Price & Predictions":
                self.visualize_price_predictions()
            elif chart_type == "Performance Metrics":
                self.visualize_performance_metrics()
            elif chart_type == "Attention Weights":
                self.visualize_attention_weights()
            else:
                raise ValueError(f"Unsupported chart type: {chart_type}")

            self.status_var.set(f"{chart_type} chart updated")
            logger.info(f"Updated {chart_type} chart")

        except Exception as e:
            error_msg = f"Error updating chart: {str(e)}"
            self.status_var.set(error_msg)
            logger.error(error_msg)
            messagebox.showerror("Chart Error", error_msg)

    def visualize_price_predictions(self):
        """Visualize price predictions."""
        # Check if data is preprocessed
        if not hasattr(self, 'X_val') or self.X_val is None:
            messagebox.showerror("Error", "Please preprocess data first")
            return

        # Make predictions on validation data
        model_type = self.model_type_var.get()

        if model_type == 'ARIMA':
            # For ARIMA, use the last value of each sequence
            close_idx = self.feature_cols.index('close') if 'close' in self.feature_cols else 0
            last_values = self.X_val[:, -1, close_idx]
            y_pred = self.model.predict(last_values).flatten()
        else:
            y_pred = self.model.predict(self.X_val).flatten()

        # Create a figure
        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot actual vs predicted
        ax.plot(self.y_val, label='Actual')
        ax.plot(y_pred, label='Predicted')
        ax.set_title('Actual vs Predicted Returns')
        ax.set_xlabel('Sample')
        ax.set_ylabel('Return')
        ax.legend()
        ax.grid(True)

        # Create a canvas to display the figure
        canvas = FigureCanvasTkAgg(fig, master=self.chart_display_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def visualize_performance_metrics(self):
        """Visualize model performance metrics."""
        # Check if data is preprocessed
        if not hasattr(self, 'X_val') or self.X_val is None:
            messagebox.showerror("Error", "Please preprocess data first")
            return

        # Evaluate model on validation data
        if not hasattr(self.model, 'evaluate'):
            messagebox.showerror("Error", "Model does not support evaluation")
            return

        # Get feature columns
        feature_cols = self.feature_cols if hasattr(self, 'feature_cols') else None

        # Evaluate model
        metrics = self.model.evaluate(self.X_val, self.y_val, feature_cols)

        # Create a figure
        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot metrics as a bar chart
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())

        # Sort metrics by value
        sorted_indices = np.argsort(metric_values)
        metric_names = [metric_names[i] for i in sorted_indices]
        metric_values = [metric_values[i] for i in sorted_indices]

        ax.barh(metric_names, metric_values)
        ax.set_title('Model Performance Metrics')
        ax.set_xlabel('Value')
        ax.grid(True, axis='x')

        # Add value labels
        for i, v in enumerate(metric_values):
            ax.text(v, i, f"{v:.4f}", va='center')

        # Create a canvas to display the figure
        canvas = FigureCanvasTkAgg(fig, master=self.chart_display_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def visualize_attention_weights(self):
        """Visualize attention weights for TFT model."""
        model_type = self.model_type_var.get()

        if model_type != 'TFT':
            messagebox.showerror("Error", "Attention weights visualization is only available for TFT models")
            return

        # Check if data is preprocessed
        if not hasattr(self, 'X_val') or self.X_val is None:
            messagebox.showerror("Error", "Please preprocess data first")
            return

        # Check if model has visualize_attention method
        if not hasattr(self.model, 'visualize_attention'):
            messagebox.showerror("Error", "Model does not support attention visualization")
            return

        # Create a temporary file for the visualization
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            temp_file = tmp.name

        # Visualize attention weights
        self.model.visualize_attention(self.X_val[:3], save_path=temp_file)

        # Load the image
        from PIL import Image, ImageTk
        img = Image.open(temp_file)
        img = img.resize((800, 600), Image.LANCZOS)
        img_tk = ImageTk.PhotoImage(img)

        # Display the image
        label = ttk.Label(self.chart_display_frame, image=img_tk)
        label.image = img_tk  # Keep a reference to prevent garbage collection
        label.pack(fill=tk.BOTH, expand=True)

        # Clean up the temporary file
        import os
        os.unlink(temp_file)

def main():
    """Main function to run the Trading Bot GUI."""
    root = tk.Tk()
    # Create the application instance
    # The variable is not used directly but is needed to keep the instance alive
    _ = TradingBotGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
