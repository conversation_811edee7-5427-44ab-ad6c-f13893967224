#!/usr/bin/env python3
"""
Comprehensive Logging and Monitoring System

This module provides a comprehensive logging system with:
- Separate logs for each terminal
- Readable main.log with structured format
- Performance tracking per terminal
- Real-time monitoring capabilities
- Log rotation and management
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional, List
import json
from datetime import datetime, timedelta
import threading
from dataclasses import dataclass, asdict
import time

@dataclass
class TerminalMetrics:
    """Metrics for a specific terminal."""
    terminal_id: str
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_profit: float = 0.0
    current_balance: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    last_trade_time: Optional[str] = None
    last_signal: Optional[str] = None
    active_positions: int = 0
    errors_count: int = 0
    warnings_count: int = 0
    status: str = "INACTIVE"
    last_update: str = ""

class ComprehensiveLogger:
    """Comprehensive logging system for trading bot operations."""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)

        # Create subdirectories
        self.terminals_dir = self.log_dir / "terminals"
        self.performance_dir = self.log_dir / "performance"
        self.monitoring_dir = self.log_dir / "monitoring"
        self.trading_history_dir = self.log_dir / "trading_history"

        for dir_path in [self.terminals_dir, self.performance_dir, self.monitoring_dir, self.trading_history_dir]:
            dir_path.mkdir(exist_ok=True)

        # Create trading history subdirectories for each terminal
        for terminal_id in range(1, 6):
            terminal_history_dir = self.trading_history_dir / f"terminal_{terminal_id}"
            terminal_history_dir.mkdir(exist_ok=True)

            # Create subdirectories for different types of trading history
            for subdir in ["trades", "signals", "performance", "mt5_history"]:
                (terminal_history_dir / subdir).mkdir(exist_ok=True)
        
        # Terminal metrics tracking
        self.terminal_metrics: Dict[str, TerminalMetrics] = {}
        self.metrics_lock = threading.RLock()
        
        # Logger instances
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, List[logging.Handler]] = {}
        
        # Setup main logger
        self._setup_main_logger()
        
        # Setup monitoring
        self.monitoring_active = False
        self.monitoring_thread = None

        # Setup trading history logging
        self._setup_trading_history_logging()
        
    def _setup_main_logger(self) -> None:
        """Setup the main readable log file."""
        main_logger = logging.getLogger("main")
        main_logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        main_logger.handlers.clear()
        
        # Main log file with rotation
        main_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "main.log",
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        
        # Readable format for main log
        main_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        main_handler.setFormatter(main_formatter)
        main_logger.addHandler(main_handler)
        
        # Console handler for main logger
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        main_logger.addHandler(console_handler)
        
        self.loggers["main"] = main_logger
        self.handlers["main"] = [main_handler, console_handler]
        
    def setup_terminal_logger(self, terminal_id: str) -> logging.Logger:
        """Setup logging for a specific terminal."""
        logger_name = f"terminal_{terminal_id}"
        
        if logger_name in self.loggers:
            return self.loggers[logger_name]
        
        # Create terminal logger
        terminal_logger = logging.getLogger(logger_name)
        terminal_logger.setLevel(logging.INFO)
        terminal_logger.handlers.clear()
        
        # Terminal-specific log file
        terminal_log_file = self.terminals_dir / f"terminal_{terminal_id}.log"
        terminal_handler = logging.handlers.RotatingFileHandler(
            terminal_log_file,
            maxBytes=20*1024*1024,  # 20MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # Detailed format for terminal logs (remove terminal_id from format to avoid KeyError)
        terminal_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | [T' + str(terminal_id) + '] | %(funcName)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        terminal_handler.setFormatter(terminal_formatter)
        terminal_logger.addHandler(terminal_handler)
        
        # Performance log for this terminal
        perf_log_file = self.performance_dir / f"terminal_{terminal_id}_performance.log"
        perf_handler = logging.handlers.RotatingFileHandler(
            perf_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=3,
            encoding='utf-8'
        )
        
        perf_formatter = logging.Formatter(
            '%(asctime)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        perf_handler.setFormatter(perf_formatter)
        
        # Create performance logger
        perf_logger = logging.getLogger(f"performance_{terminal_id}")
        perf_logger.setLevel(logging.INFO)
        perf_logger.handlers.clear()
        perf_logger.addHandler(perf_handler)
        
        self.loggers[logger_name] = terminal_logger
        self.loggers[f"performance_{terminal_id}"] = perf_logger
        self.handlers[logger_name] = [terminal_handler]
        self.handlers[f"performance_{terminal_id}"] = [perf_handler]
        
        # Initialize terminal metrics
        with self.metrics_lock:
            if terminal_id not in self.terminal_metrics:
                self.terminal_metrics[terminal_id] = TerminalMetrics(
                    terminal_id=terminal_id,
                    last_update=datetime.now().isoformat()
                )
        
        return terminal_logger

    def _setup_trading_history_logging(self) -> None:
        """Setup dedicated trading history logging for all terminals."""
        try:
            # Create trading history loggers for each terminal
            for terminal_id in range(1, 6):
                # Trading history logger
                history_logger_name = f"trading_history_terminal_{terminal_id}"
                history_logger = logging.getLogger(history_logger_name)
                history_logger.setLevel(logging.INFO)

                # Clear existing handlers
                history_logger.handlers.clear()

                # Trading history file handler
                history_log_file = self.trading_history_dir / f"terminal_{terminal_id}" / "trading_history.log"
                history_handler = logging.handlers.RotatingFileHandler(
                    history_log_file,
                    maxBytes=50*1024*1024,  # 50MB
                    backupCount=10,
                    encoding='utf-8'
                )

                # Trading history format
                history_formatter = logging.Formatter(
                    '%(asctime)s | %(levelname)-8s | [T' + str(terminal_id) + '] | %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                history_handler.setFormatter(history_formatter)
                history_logger.addHandler(history_handler)

                # Store logger and handler
                self.loggers[history_logger_name] = history_logger
                if f"trading_history_terminal_{terminal_id}" not in self.handlers:
                    self.handlers[f"trading_history_terminal_{terminal_id}"] = []
                self.handlers[f"trading_history_terminal_{terminal_id}"].append(history_handler)

            # Create consolidated trading history logger
            consolidated_logger_name = "consolidated_trading_history"
            consolidated_logger = logging.getLogger(consolidated_logger_name)
            consolidated_logger.setLevel(logging.INFO)
            consolidated_logger.handlers.clear()

            # Consolidated trading history file
            consolidated_log_file = self.trading_history_dir / "consolidated_trading_history.log"
            consolidated_handler = logging.handlers.RotatingFileHandler(
                consolidated_log_file,
                maxBytes=100*1024*1024,  # 100MB
                backupCount=20,
                encoding='utf-8'
            )

            # Consolidated format
            consolidated_formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            consolidated_handler.setFormatter(consolidated_formatter)
            consolidated_logger.addHandler(consolidated_handler)

            # Store consolidated logger and handler
            self.loggers[consolidated_logger_name] = consolidated_logger
            self.handlers[consolidated_logger_name] = [consolidated_handler]

        except Exception as e:
            print(f"Error setting up trading history logging: {e}")

    def get_trading_history_logger(self, terminal_id: str) -> logging.Logger:
        """Get trading history logger for a specific terminal."""
        logger_name = f"trading_history_terminal_{terminal_id}"
        if logger_name in self.loggers:
            return self.loggers[logger_name]
        else:
            # Fallback to terminal logger
            return self.get_terminal_logger(terminal_id)

    def get_consolidated_trading_history_logger(self) -> logging.Logger:
        """Get consolidated trading history logger."""
        logger_name = "consolidated_trading_history"
        if logger_name in self.loggers:
            return self.loggers[logger_name]
        else:
            # Fallback to main logger
            return self.get_main_logger()

    def get_terminal_logger(self, terminal_id: str) -> logging.Logger:
        """Get logger for a specific terminal."""
        logger_name = f"terminal_{terminal_id}"
        if logger_name not in self.loggers:
            return self.setup_terminal_logger(terminal_id)
        return self.loggers[logger_name]
    
    def get_performance_logger(self, terminal_id: str) -> logging.Logger:
        """Get performance logger for a specific terminal."""
        logger_name = f"performance_{terminal_id}"
        if logger_name not in self.loggers:
            self.setup_terminal_logger(terminal_id)  # This creates both loggers
        return self.loggers[logger_name]
    
    def get_main_logger(self) -> logging.Logger:
        """Get the main logger."""
        return self.loggers["main"]
    
    def log_trade_execution(self, terminal_id: str, trade_data: Dict[str, Any]) -> None:
        """Log trade execution with comprehensive details."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        perf_logger = self.get_performance_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        # Extract trade details
        action = trade_data.get('action', 'unknown')
        symbol = trade_data.get('symbol', 'unknown')
        volume = trade_data.get('volume', 0.0)
        price = trade_data.get('price', 0.0)
        profit = trade_data.get('profit_loss', 0.0)
        ticket = trade_data.get('ticket', 'unknown')
        
        # Log to terminal-specific log
        terminal_logger.info(
            f"TRADE_EXECUTED | Action: {action.upper()} | Symbol: {symbol} | "
            f"Volume: {volume} | Price: {price} | Ticket: {ticket} | P&L: {profit:.2f}",
            extra={'terminal_id': terminal_id}
        )
        
        # Log to performance log
        perf_logger.info(
            f"TRADE | {action.upper()} | {symbol} | Vol: {volume} | "
            f"Price: {price} | P&L: {profit:.2f} | Ticket: {ticket}"
        )
        
        # Log to main log (summary)
        main_logger.info(
            f"[T{terminal_id}] Trade executed: {action.upper()} {volume} {symbol} "
            f"at {price} (P&L: {profit:.2f})"
        )
        
        # Update metrics
        self.update_terminal_metrics(terminal_id, trade_data)
    
    def log_signal_generation(self, terminal_id: str, signal_data: Dict[str, Any]) -> None:
        """Log signal generation details."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        signal_type = signal_data.get('direction', 'unknown')
        confidence = signal_data.get('confidence', 0.0)
        symbol = signal_data.get('symbol', 'unknown')
        
        terminal_logger.info(
            f"SIGNAL_GENERATED | Type: {signal_type} | Symbol: {symbol} | "
            f"Confidence: {confidence:.3f} | Models: {signal_data.get('models_used', [])}",
            extra={'terminal_id': terminal_id}
        )
        
        main_logger.info(
            f"[T{terminal_id}] Signal: {signal_type} for {symbol} "
            f"(confidence: {confidence:.3f})"
        )
        
        # Update metrics
        with self.metrics_lock:
            if terminal_id in self.terminal_metrics:
                self.terminal_metrics[terminal_id].last_signal = signal_type
                self.terminal_metrics[terminal_id].last_update = datetime.now().isoformat()
    
    def log_error(self, terminal_id: str, error_msg: str, error_type: str = "ERROR") -> None:
        """Log errors with proper categorization."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        terminal_logger.error(
            f"{error_type} | {error_msg}",
            extra={'terminal_id': terminal_id}
        )
        
        main_logger.error(f"[T{terminal_id}] {error_type}: {error_msg}")
        
        # Update error count
        with self.metrics_lock:
            if terminal_id in self.terminal_metrics:
                self.terminal_metrics[terminal_id].errors_count += 1
                self.terminal_metrics[terminal_id].last_update = datetime.now().isoformat()
    
    def log_warning(self, terminal_id: str, warning_msg: str) -> None:
        """Log warnings."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        terminal_logger.warning(
            f"WARNING | {warning_msg}",
            extra={'terminal_id': terminal_id}
        )
        
        main_logger.warning(f"[T{terminal_id}] WARNING: {warning_msg}")
        
        # Update warning count
        with self.metrics_lock:
            if terminal_id in self.terminal_metrics:
                self.terminal_metrics[terminal_id].warnings_count += 1
                self.terminal_metrics[terminal_id].last_update = datetime.now().isoformat()
    
    def update_terminal_metrics(self, terminal_id: str, data: Dict[str, Any]) -> None:
        """Update metrics for a terminal."""
        with self.metrics_lock:
            if terminal_id not in self.terminal_metrics:
                self.terminal_metrics[terminal_id] = TerminalMetrics(terminal_id=terminal_id)
            
            metrics = self.terminal_metrics[terminal_id]
            
            # Update trade counts
            if 'action' in data:
                metrics.total_trades += 1
                metrics.last_trade_time = datetime.now().isoformat()
                
                profit = data.get('profit_loss', 0.0)
                metrics.total_profit += profit
                
                if profit > 0:
                    metrics.winning_trades += 1
                elif profit < 0:
                    metrics.losing_trades += 1
                
                # Calculate win rate
                if metrics.total_trades > 0:
                    metrics.win_rate = (metrics.winning_trades / metrics.total_trades) * 100
            
            # Update other metrics
            if 'balance' in data:
                metrics.current_balance = data['balance']
            
            if 'drawdown' in data:
                metrics.max_drawdown = max(metrics.max_drawdown, data['drawdown'])
            
            if 'active_positions' in data:
                metrics.active_positions = data['active_positions']
            
            if 'status' in data:
                metrics.status = data['status']
            
            metrics.last_update = datetime.now().isoformat()
    
    def get_terminal_metrics(self, terminal_id: str) -> Optional[TerminalMetrics]:
        """Get metrics for a specific terminal."""
        with self.metrics_lock:
            return self.terminal_metrics.get(terminal_id)
    
    def get_all_metrics(self) -> Dict[str, TerminalMetrics]:
        """Get metrics for all terminals."""
        with self.metrics_lock:
            return self.terminal_metrics.copy()
    
    def save_metrics_snapshot(self) -> None:
        """Save current metrics to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = self.monitoring_dir / f"metrics_snapshot_{timestamp}.json"
        
        with self.metrics_lock:
            metrics_data = {
                'timestamp': datetime.now().isoformat(),
                'terminals': {
                    terminal_id: asdict(metrics)
                    for terminal_id, metrics in self.terminal_metrics.items()
                }
            }
        
        with open(metrics_file, 'w') as f:
            json.dump(metrics_data, f, indent=2, default=str)
    
    def start_monitoring(self, interval: int = 60) -> None:
        """Start background monitoring and metrics collection."""
        if self.monitoring_active:
            return

        self.monitoring_active = True

        def monitoring_loop():
            # Prevent logging loops by using a simple counter
            loop_count = 0
            while self.monitoring_active:
                try:
                    # Save metrics snapshot every 10 loops (reduce frequency)
                    if loop_count % 10 == 0:
                        self.save_metrics_snapshot()

                    # Log summary to main log only once per hour to prevent loops
                    if loop_count % 60 == 0:  # Once per hour if interval is 60 seconds
                        main_logger = self.get_main_logger()
                        with self.metrics_lock:
                            active_terminals = len([
                                m for m in self.terminal_metrics.values()
                                if m.status == "ACTIVE"
                            ])
                            total_trades = sum(m.total_trades for m in self.terminal_metrics.values())
                            total_profit = sum(m.total_profit for m in self.terminal_metrics.values())

                        main_logger.info(
                            f"MONITORING | Active Terminals: {active_terminals} | "
                            f"Total Trades: {total_trades} | Total P&L: {total_profit:.2f}"
                        )

                    loop_count += 1
                    time.sleep(interval)

                except Exception as e:
                    # Use print instead of logger to prevent loops
                    print(f"Error in monitoring loop: {str(e)}")
                    time.sleep(interval)

        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
    
    def cleanup(self) -> None:
        """Cleanup logging resources."""
        self.stop_monitoring()
        
        # Close all handlers
        for handlers_list in self.handlers.values():
            for handler in handlers_list:
                handler.close()
        
        # Clear loggers
        self.loggers.clear()
        self.handlers.clear()

# Global instance
comprehensive_logger = ComprehensiveLogger()

def get_terminal_logger(terminal_id: str) -> logging.Logger:
    """Get logger for a specific terminal."""
    return comprehensive_logger.get_terminal_logger(terminal_id)

def get_main_logger() -> logging.Logger:
    """Get the main logger."""
    return comprehensive_logger.get_main_logger()

def log_trade(terminal_id: str, trade_data: Dict[str, Any]) -> None:
    """Log trade execution."""
    comprehensive_logger.log_trade_execution(terminal_id, trade_data)

def log_signal(terminal_id: str, signal_data: Dict[str, Any]) -> None:
    """Log signal generation."""
    comprehensive_logger.log_signal_generation(terminal_id, signal_data)

def log_terminal_error(terminal_id: str, error_msg: str, error_type: str = "ERROR") -> None:
    """Log terminal-specific error."""
    comprehensive_logger.log_error(terminal_id, error_msg, error_type)

def log_terminal_warning(terminal_id: str, warning_msg: str) -> None:
    """Log terminal-specific warning."""
    comprehensive_logger.log_warning(terminal_id, warning_msg)

def update_metrics(terminal_id: str, data: Dict[str, Any]) -> None:
    """Update terminal metrics."""
    comprehensive_logger.update_terminal_metrics(terminal_id, data)

def start_monitoring(interval: int = 60) -> None:
    """Start monitoring system - prevent duplicate calls."""
    if not hasattr(comprehensive_logger, '_monitoring_started'):
        comprehensive_logger.start_monitoring(interval)
        comprehensive_logger._monitoring_started = True

def stop_monitoring() -> None:
    """Stop monitoring system."""
    comprehensive_logger.stop_monitoring()

def get_trading_history_logger(terminal_id: str) -> logging.Logger:
    """Get trading history logger for a specific terminal."""
    return comprehensive_logger.get_trading_history_logger(terminal_id)

def get_consolidated_trading_history_logger() -> logging.Logger:
    """Get consolidated trading history logger."""
    return comprehensive_logger.get_consolidated_trading_history_logger()

def log_trading_history(terminal_id: str, message: str, level: str = "INFO") -> None:
    """Log trading history message for a specific terminal."""
    history_logger = get_trading_history_logger(terminal_id)
    consolidated_logger = get_consolidated_trading_history_logger()

    # Log to terminal-specific trading history
    if level.upper() == "ERROR":
        history_logger.error(message)
        consolidated_logger.error(f"[T{terminal_id}] {message}")
    elif level.upper() == "WARNING":
        history_logger.warning(message)
        consolidated_logger.warning(f"[T{terminal_id}] {message}")
    else:
        history_logger.info(message)
        consolidated_logger.info(f"[T{terminal_id}] {message}")
